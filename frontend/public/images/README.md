# 📸 Structure des Images - Imlil Bio Boutique

## 📁 Organisation des Dossiers

```
frontend/public/images/
├── products/           # Images des produits
├── categories/         # Images des catégories
└── banners/           # Bannières et images promotionnelles
```

## 🛍️ **Products** (`/products/`)

Images haute qualité des produits individuels. Chaque produit peut avoir plusieurs images.

### Conventions de Nommage :
- Format : `{nom-produit}-{numero}.jpg`
- Exemple : `amlou-traditionnel-1.jpg`, `amlou-traditionnel-2.jpg`

### Images Actuelles :
- **Amlou** :
  - `amlou-traditionnel-1.jpg`
  - `amlou-traditionnel-2.jpg`

- **<PERSON>le d'Argan** :
  - `huile-argan-cosmetique-1.jpg`
  - `huile-argan-cosmetique-2.jpg`
  - `huile-argan-cosmetique-3.jpg`
  - `huile-argan-culinaire-1.jpg`
  - `huile-argan-culinaire-2.jpg`

- **Miel** :
  - `miel-montagne-1.jpg`
  - `miel-acacia-1.jpg`
  - `miel-acacia-2.jpg`

## 🏷️ **Categories** (`/categories/`)

Images représentatives de chaque catégorie de produits.

### Images Actuelles :
- `amlou-category.jpg` - Image de la catégorie Amlou
- `honey-category.jpg` - Image de la catégorie Miel
- `argan-oil-category.jpg` - Image de la catégorie Huile d'Argan

## 🎨 **Banners** (`/banners/`)

Bannières pour la page d'accueil, promotions et sections spéciales.

### Images Actuelles :
- `hero-banner-1.jpg` - Bannière principale du hero
- `promo-banner.jpg` - Bannière promotionnelle

## 📋 **Spécifications Techniques**

### Formats Recommandés :
- **Format** : JPG, PNG, WebP
- **Qualité** : Haute résolution pour les produits
- **Optimisation** : Compressées pour le web

### Tailles Recommandées :

#### Images Produits :
- **Principale** : 800x800px (carré)
- **Galerie** : 600x600px minimum
- **Miniatures** : 300x300px

#### Images Catégories :
- **Taille** : 400x300px (4:3)
- **Style** : Représentative de la catégorie

#### Bannières :
- **Hero Banner** : 1920x600px (16:5)
- **Promo Banner** : 1200x400px (3:1)

## 🔗 **Utilisation dans le Code**

### Référencement des Images :
```typescript
// Images produits
const productImage = "/images/products/amlou-traditionnel-1.jpg";

// Images catégories
const categoryImage = "/images/categories/amlou-category.jpg";

// Bannières
const heroBanner = "/images/banners/hero-banner-1.jpg";
```

### Composants React :
```jsx
// Image produit
<img src="/images/products/amlou-traditionnel-1.jpg" alt="Amlou Traditionnel" />

// Image catégorie
<img src="/images/categories/honey-category.jpg" alt="Catégorie Miel" />

// Bannière
<img src="/images/banners/hero-banner-1.jpg" alt="Bannière Hero" />
```

## 📝 **Notes Importantes**

1. **Chemins Absolus** : Toujours utiliser des chemins commençant par `/images/`
2. **Alt Text** : Toujours inclure un texte alternatif descriptif
3. **Optimisation** : Compresser les images avant de les ajouter
4. **Nommage** : Utiliser des noms descriptifs en kebab-case
5. **Versions** : Numéroter les variantes d'un même produit

## 🚀 **Ajout de Nouvelles Images**

### Pour un Nouveau Produit :
1. Créer les images dans `/products/`
2. Nommer selon la convention : `{nom-produit}-{numero}.jpg`
3. Mettre à jour la base de données avec les chemins corrects

### Pour une Nouvelle Catégorie :
1. Ajouter l'image dans `/categories/`
2. Nommer : `{nom-categorie}-category.jpg`
3. Référencer dans les composants de navigation

### Pour une Nouvelle Bannière :
1. Ajouter dans `/banners/`
2. Respecter les dimensions recommandées
3. Optimiser pour le web

---

**Structure Créée** : 3 juillet 2025  
**Statut** : ✅ OPÉRATIONNELLE  
**Images** : 🎯 PRÊTES POUR INTÉGRATION
