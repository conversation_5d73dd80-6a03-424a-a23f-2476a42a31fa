# Guide de Gestion des Images - E-commerce

Ce guide explique les différentes stratégies pour stocker et gérer les images de produits dans votre application e-commerce.

## 🖼️ Stratégies de Stockage d'Images

### 1. 📁 Stockage Local (Développement)
**Avantages :**
- Simple à mettre en place
- Pas de coûts externes
- Contrôle total des fichiers

**Inconvénients :**
- Pas adapté pour la production
- Problèmes de performance
- Pas de CDN intégré

**Structure recommandée :**
```
frontend/public/images/
├── products/
│   ├── amlou-traditionnel-1.jpg
│   ├── amlou-traditionnel-2.jpg
│   ├── miel-montagne-1.jpg
│   └── ...
├── categories/
│   ├── amlou-category.jpg
│   ├── honey-category.jpg
│   └── argan-oil-category.jpg
└── banners/
    ├── hero-banner-1.jpg
    └── promo-banner.jpg
```

### 2. ☁️ Cloudinary (Recommandé pour Production)
**Avantages :**
- CDN mondial intégré
- Optimisation automatique des images
- Redimensionnement à la volée
- Transformations avancées
- Plan gratuit généreux

**Configuration :**
```javascript
// backend/src/config/cloudinary.ts
import { v2 as cloudinary } from 'cloudinary';

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

export default cloudinary;
```

### 3. 🪣 AWS S3 (Enterprise)
**Avantages :**
- Très haute disponibilité
- Intégration avec CloudFront CDN
- Contrôle granulaire des permissions
- Évolutivité illimitée

**Inconvénients :**
- Configuration plus complexe
- Coûts variables selon l'usage

## 🔧 Implémentation Recommandée

### Configuration Cloudinary

#### 1. Installation
```bash
npm install cloudinary multer multer-storage-cloudinary
```

#### 2. Variables d'Environnement
```env
# backend/.env
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

#### 3. Service d'Upload
```typescript
// backend/src/services/imageService.ts
import cloudinary from '../config/cloudinary.js';
import { UploadApiResponse } from 'cloudinary';

export class ImageService {
  static async uploadProductImage(
    file: Express.Multer.File,
    productId: string
  ): Promise<string> {
    try {
      const result: UploadApiResponse = await cloudinary.uploader.upload(
        file.path,
        {
          folder: `products/${productId}`,
          public_id: `${productId}-${Date.now()}`,
          transformation: [
            { width: 800, height: 800, crop: 'fill', quality: 'auto' },
            { format: 'webp' }
          ]
        }
      );
      
      return result.secure_url;
    } catch (error) {
      throw new Error(`Image upload failed: ${error.message}`);
    }
  }

  static async deleteImage(publicId: string): Promise<void> {
    try {
      await cloudinary.uploader.destroy(publicId);
    } catch (error) {
      console.error('Image deletion failed:', error);
    }
  }

  static generateImageUrl(
    publicId: string,
    width: number = 400,
    height: number = 400
  ): string {
    return cloudinary.url(publicId, {
      width,
      height,
      crop: 'fill',
      quality: 'auto',
      format: 'webp'
    });
  }
}
```

#### 4. Route d'Upload
```typescript
// backend/src/routes/uploadRoutes.ts
import express from 'express';
import multer from 'multer';
import { ImageService } from '../services/imageService.js';

const router = express.Router();
const upload = multer({ dest: 'uploads/' });

router.post('/product-image/:productId', 
  upload.single('image'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ 
          success: false, 
          message: 'No image file provided' 
        });
      }

      const imageUrl = await ImageService.uploadProductImage(
        req.file,
        req.params.productId
      );

      res.json({
        success: true,
        message: 'Image uploaded successfully',
        data: { imageUrl }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }
);

export default router;
```

### Frontend - Composant d'Upload

```tsx
// frontend/src/components/ImageUpload.tsx
import React, { useState } from 'react';

interface ImageUploadProps {
  productId: string;
  onImageUploaded: (imageUrl: string) => void;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({
  productId,
  onImageUploaded
}) => {
  const [uploading, setUploading] = useState(false);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    
    const formData = new FormData();
    formData.append('image', file);

    try {
      const response = await fetch(`/api/upload/product-image/${productId}`, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        onImageUploaded(result.data.imageUrl);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      alert('Erreur lors de l\'upload de l\'image');
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="image-upload">
      <input
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        disabled={uploading}
        className="hidden"
        id="image-upload"
      />
      <label
        htmlFor="image-upload"
        className={`
          cursor-pointer inline-flex items-center px-4 py-2 
          border border-gray-300 rounded-md shadow-sm text-sm 
          font-medium text-gray-700 bg-white hover:bg-gray-50
          ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        {uploading ? 'Upload en cours...' : 'Choisir une image'}
      </label>
    </div>
  );
};
```

## 📐 Optimisation des Images

### Tailles Recommandées
```typescript
// Configuration des tailles d'images
export const IMAGE_SIZES = {
  thumbnail: { width: 150, height: 150 },
  card: { width: 300, height: 300 },
  detail: { width: 600, height: 600 },
  hero: { width: 1200, height: 600 },
  gallery: { width: 800, height: 800 }
};

// Génération d'URLs optimisées
export const getOptimizedImageUrl = (
  baseUrl: string,
  size: keyof typeof IMAGE_SIZES
): string => {
  const { width, height } = IMAGE_SIZES[size];
  
  if (baseUrl.includes('cloudinary.com')) {
    return baseUrl.replace(
      '/upload/',
      `/upload/w_${width},h_${height},c_fill,q_auto,f_webp/`
    );
  }
  
  return baseUrl; // Fallback pour les images locales
};
```

### Composant Image Optimisé
```tsx
// frontend/src/components/OptimizedImage.tsx
import React, { useState } from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  size: 'thumbnail' | 'card' | 'detail' | 'hero' | 'gallery';
  className?: string;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  size,
  className = ''
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const optimizedSrc = getOptimizedImageUrl(src, size);

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
      )}
      
      {hasError ? (
        <div className="flex items-center justify-center bg-gray-100 text-gray-400">
          <span>Image non disponible</span>
        </div>
      ) : (
        <img
          src={optimizedSrc}
          alt={alt}
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setIsLoading(false);
            setHasError(true);
          }}
          className={`
            transition-opacity duration-300
            ${isLoading ? 'opacity-0' : 'opacity-100'}
          `}
        />
      )}
    </div>
  );
};
```

## 🔄 Migration des Images Existantes

### Script de Migration
```typescript
// backend/src/scripts/migrateImages.ts
import { Product } from '../models/Product.js';
import { ImageService } from '../services/imageService.js';
import fs from 'fs';
import path from 'path';

export async function migrateLocalImagesToCloudinary() {
  const products = await Product.find();
  
  for (const product of products) {
    const updatedImages: string[] = [];
    
    for (const imagePath of product.images) {
      if (imagePath.startsWith('/images/')) {
        // Image locale à migrer
        const localPath = path.join(process.cwd(), 'public', imagePath);
        
        if (fs.existsSync(localPath)) {
          try {
            const cloudinaryUrl = await ImageService.uploadProductImage(
              { path: localPath } as Express.Multer.File,
              product._id.toString()
            );
            updatedImages.push(cloudinaryUrl);
          } catch (error) {
            console.error(`Failed to migrate ${imagePath}:`, error);
            updatedImages.push(imagePath); // Garder l'original en cas d'erreur
          }
        }
      } else {
        // Image déjà sur Cloudinary
        updatedImages.push(imagePath);
      }
    }
    
    product.images = updatedImages;
    await product.save();
    
    console.log(`Migrated images for product: ${product.title.fr}`);
  }
}
```

## 📊 Monitoring et Analytics

### Métriques à Surveiller
1. **Taille des images** : Optimisation continue
2. **Temps de chargement** : Performance utilisateur
3. **Coûts de stockage** : Budget cloud
4. **Taux d'erreur** : Disponibilité des images

### Outils de Monitoring
```typescript
// Service de monitoring des images
export class ImageMonitoringService {
  static async checkImageAvailability(imageUrl: string): Promise<boolean> {
    try {
      const response = await fetch(imageUrl, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  static async auditProductImages() {
    const products = await Product.find();
    const report = {
      totalProducts: products.length,
      totalImages: 0,
      brokenImages: 0,
      missingImages: []
    };

    for (const product of products) {
      report.totalImages += product.images.length;
      
      for (const imageUrl of product.images) {
        const isAvailable = await this.checkImageAvailability(imageUrl);
        if (!isAvailable) {
          report.brokenImages++;
          report.missingImages.push({
            productId: product._id,
            productName: product.title.fr,
            imageUrl
          });
        }
      }
    }

    return report;
  }
}
```

## 🎯 Recommandations Finales

### Pour le Développement
1. **Utilisez le stockage local** pour commencer
2. **Préparez la structure** pour Cloudinary
3. **Testez avec des images de placeholder**

### Pour la Production
1. **Migrez vers Cloudinary** dès que possible
2. **Configurez les transformations** automatiques
3. **Surveillez les performances** et coûts
4. **Implémentez la compression** WebP
5. **Utilisez un CDN** pour la distribution mondiale

Votre système de gestion d'images est maintenant prêt pour évoluer avec votre application ! 🚀
