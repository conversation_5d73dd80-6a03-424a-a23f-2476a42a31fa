# Guide MongoDB Atlas - Interface Web

Ce guide vous explique comment accéder et naviguer dans votre base de données MongoDB Atlas via l'interface web.

## 🔗 Accès à MongoDB Atlas

### 1. Connexion à MongoDB Atlas
1. Rendez-vous sur [https://cloud.mongodb.com/](https://cloud.mongodb.com/)
2. Connectez-vous avec vos identifiants :
   - **Email** : Votre email d'inscription
   - **Mot de passe** : Votre mot de passe MongoDB Atlas

### 2. Sélection du Cluster
1. Une fois connecté, vous verrez votre tableau de bord
2. Localisez votre cluster : **"Soufiane-boukil-bio-cluster"**
3. Cliquez sur **"Browse Collections"** pour accéder aux données

## 📊 Navigation dans les Collections

### Collections Principales
Votre base de données contient les collections suivantes :

#### 📦 **Products** (Produits)
- **Nombre d'éléments** : 6 produits
- **Structure** :
  - `title` : Titre multilingue (ar, fr, en)
  - `description` : Description multilingue
  - `price` : Prix actuel
  - `originalPrice` : Prix original
  - `category` : Catégorie (amlou, honey, argan)
  - `images` : Tableau d'URLs d'images
  - `tags` : Mots-clés pour la recherche
  - `stockQuantity` : Quantité en stock

#### 👥 **Customers** (Clients)
- **Nombre d'éléments** : 3 clients de test
- **Structure** :
  - `firstName`, `lastName` : Nom et prénom
  - `email` : Adresse email unique
  - `phone` : Numéro de téléphone
  - `preferredLanguage` : Langue préférée (ar, fr, en)
  - `totalOrders`, `totalSpent` : Statistiques d'achat

#### 🛒 **Orders** (Commandes)
- **Nombre d'éléments** : 0 (vide pour l'instant)
- **Structure** : Prête pour les futures commandes

## 🔍 Exploration des Données

### Visualisation des Documents
1. **Cliquez sur une collection** (ex: "products")
2. **Parcourez les documents** avec les boutons de navigation
3. **Examinez la structure** de chaque document
4. **Utilisez les filtres** pour rechercher des données spécifiques

### Exemples de Produits Créés
```json
{
  "_id": "...",
  "title": {
    "ar": "اللوز التقليدي",
    "fr": "Amlou Traditionnel",
    "en": "Traditional Amlou"
  },
  "price": 120,
  "category": "amlou",
  "stockQuantity": 50,
  "featured": true
}
```

## 🛠️ Outils de Gestion

### Filtrage et Recherche
1. **Barre de filtre** : Utilisez la syntaxe MongoDB
   ```javascript
   // Rechercher les produits en promotion
   { "featured": true }
   
   // Rechercher par catégorie
   { "category": "amlou" }
   
   // Rechercher par prix
   { "price": { "$lt": 200 } }
   ```

2. **Tri des résultats** : Cliquez sur les en-têtes de colonnes

### Modification des Données
1. **Éditer un document** : Cliquez sur l'icône crayon
2. **Ajouter un document** : Bouton "Insert Document"
3. **Supprimer un document** : Icône corbeille (⚠️ Attention !)

## 📈 Surveillance et Statistiques

### Métriques du Cluster
1. **Onglet "Metrics"** : Performances en temps réel
2. **Utilisation du stockage** : Espace utilisé/disponible
3. **Connexions actives** : Nombre de connexions à la base

### Logs et Activité
1. **Onglet "Activity Feed"** : Historique des opérations
2. **Logs de performance** : Requêtes lentes
3. **Alertes** : Notifications automatiques

## 🔧 Configuration Avancée

### Gestion des Utilisateurs
1. **Database Access** : Gérer les utilisateurs de la base
2. **Network Access** : Configurer les IP autorisées
3. **Backup** : Planifier les sauvegardes automatiques

### Sécurité
1. **Chiffrement** : Données chiffrées au repos et en transit
2. **Audit Logs** : Traçabilité des accès
3. **Compliance** : Certifications de sécurité

## 🚀 Commandes Utiles

### Via l'Interface Web
```javascript
// Compter les produits par catégorie
db.products.aggregate([
  { $group: { _id: "$category", count: { $sum: 1 } } }
])

// Trouver les produits les plus chers
db.products.find().sort({ price: -1 }).limit(5)

// Statistiques des clients
db.customers.aggregate([
  { $group: { 
    _id: null, 
    totalCustomers: { $sum: 1 },
    avgSpent: { $avg: "$totalSpent" }
  }}
])
```

## 📞 Support et Ressources

### Documentation
- [MongoDB Atlas Documentation](https://docs.atlas.mongodb.com/)
- [MongoDB Query Language](https://docs.mongodb.com/manual/tutorial/query-documents/)

### Support
- **Support MongoDB** : Via l'interface Atlas
- **Communauté** : [MongoDB Community Forums](https://developer.mongodb.com/community/forums/)

## ⚠️ Bonnes Pratiques

### Sécurité
1. **Ne jamais partager** vos identifiants de connexion
2. **Utiliser des IP whitelisting** pour limiter l'accès
3. **Sauvegarder régulièrement** vos données importantes

### Performance
1. **Surveiller les métriques** régulièrement
2. **Optimiser les requêtes** lentes
3. **Gérer les index** pour améliorer les performances

### Maintenance
1. **Mettre à jour** les versions MongoDB régulièrement
2. **Nettoyer** les données obsolètes
3. **Monitorer** l'utilisation du stockage

---

## 🎯 Prochaines Étapes

1. **Familiarisez-vous** avec l'interface MongoDB Atlas
2. **Explorez** les données créées par le script de seeding
3. **Testez** les requêtes et filtres
4. **Configurez** les alertes et la surveillance
5. **Planifiez** les sauvegardes automatiques

Votre base de données MongoDB Atlas est maintenant opérationnelle avec 6 produits et 3 clients de test ! 🎉
