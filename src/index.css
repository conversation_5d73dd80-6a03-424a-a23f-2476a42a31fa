
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }

  /* Fonts */
  @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;700&display=swap');
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');
}

@layer components {
  .container-custom {
    @apply px-4 mx-auto w-full sm:px-6 lg:px-8;
  }

  .container-full-width {
    @apply w-full px-0;
  }

  /* Animated transitions for dropdown menus */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  /* Category-specific styles */
  .honey-accent {
    @apply text-golden border-golden;
  }
  
  .honey-bg {
    @apply bg-golden text-white;
  }
  
  /* Badge styles */
  .badge-organic {
    @apply bg-green-100 text-green-800 border-green-200;
  }
  
  .badge-promo {
    @apply bg-red-100 text-red-800 border-red-200;
  }
}

@layer utilities {
  .font-arabic {
    font-family: 'Tajawal', sans-serif;
  }

  .font-title {
    font-family: 'Playfair Display', serif;
  }

  .font-body {
    font-family: 'Open Sans', sans-serif;
  }
}
