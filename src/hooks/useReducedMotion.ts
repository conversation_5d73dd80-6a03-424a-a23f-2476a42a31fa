import { useState, useEffect } from 'react';

/**
 * Hook to detect user's motion preference
 * Returns true if user prefers reduced motion
 */
export const useReducedMotion = (): boolean => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return;
    }

    // Create media query
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    // Set initial value
    setPrefersReducedMotion(mediaQuery.matches);

    // Create event handler
    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    // Add listener
    mediaQuery.addEventListener('change', handleChange);

    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return prefersReducedMotion;
};

/**
 * Utility function to get animation variants based on motion preference
 */
export const getAnimationVariants = (prefersReducedMotion: boolean) => {
  if (prefersReducedMotion) {
    return {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.1 }
    };
  }

  return {
    initial: { opacity: 0, y: 20, scale: 0.95 },
    animate: { opacity: 1, y: 0, scale: 1 },
    exit: { opacity: 0, y: -20, scale: 1.05 },
    transition: { 
      type: "spring",
      stiffness: 300,
      damping: 25,
      duration: 0.6
    }
  };
};

/**
 * Utility function to get hover animation props based on motion preference
 */
export const getHoverAnimation = (prefersReducedMotion: boolean) => {
  if (prefersReducedMotion) {
    return {};
  }

  return {
    whileHover: { 
      scale: 1.05,
      y: -2,
      transition: { 
        type: "spring",
        stiffness: 400,
        damping: 20
      }
    },
    whileTap: { scale: 0.98 }
  };
};
