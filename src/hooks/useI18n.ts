import { useLanguage } from '@/contexts/LanguageContext';
import i18nData from '@/data/i18n.json';

type Language = 'ar' | 'fr' | 'en';
type TranslationKey = string;

interface I18nHook {
  t: (key: TranslationKey, fallback?: string) => string;
  language: Language;
  isRTL: boolean;
  setLanguage: (lang: Language) => void;
}

/**
 * Enhanced i18n hook that provides translation functionality
 * with nested key support and fallback handling
 */
export const useI18n = (): I18nHook => {
  const { language, setLanguage, isRTL } = useLanguage();

  /**
   * Get translation for a given key with dot notation support
   * @param key - Translation key (e.g., 'common.loading', 'products.addToCart')
   * @param fallback - Fallback text if translation is not found
   * @returns Translated text or fallback
   */
  const t = (key: TranslationKey, fallback?: string): string => {
    try {
      // Split the key by dots to navigate nested objects
      const keys = key.split('.');
      let value: any = i18nData[language];

      // Navigate through the nested object
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          // Key not found, return fallback or key itself
          return fallback || key;
        }
      }

      // Return the found value if it's a string
      if (typeof value === 'string') {
        return value;
      }

      // If value is not a string, return fallback or key
      return fallback || key;
    } catch (error) {
      console.warn(`Translation error for key "${key}":`, error);
      return fallback || key;
    }
  };

  return {
    t,
    language,
    isRTL,
    setLanguage,
  };
};

/**
 * Helper function to get translation without using the hook
 * Useful for static contexts or outside React components
 */
export const getTranslation = (
  key: TranslationKey,
  language: Language,
  fallback?: string
): string => {
  try {
    const keys = key.split('.');
    let value: any = i18nData[language];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return fallback || key;
      }
    }

    if (typeof value === 'string') {
      return value;
    }

    return fallback || key;
  } catch (error) {
    console.warn(`Translation error for key "${key}":`, error);
    return fallback || key;
  }
};

/**
 * Helper function to check if a translation key exists
 */
export const hasTranslation = (key: TranslationKey, language: Language): boolean => {
  try {
    const keys = key.split('.');
    let value: any = i18nData[language];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return false;
      }
    }

    return typeof value === 'string';
  } catch (error) {
    return false;
  }
};

/**
 * Helper function to get all available languages
 */
export const getAvailableLanguages = (): Language[] => {
  return Object.keys(i18nData) as Language[];
};

/**
 * Helper function to get language display names
 */
export const getLanguageDisplayName = (language: Language): string => {
  const displayNames: Record<Language, string> = {
    ar: 'العربية',
    fr: 'Français',
    en: 'English',
  };
  return displayNames[language] || language;
};

/**
 * Helper function to get language flags
 */
export const getLanguageFlag = (language: Language): string => {
  const flags: Record<Language, string> = {
    ar: '🇲🇦',
    fr: '🇫🇷',
    en: '🇬🇧',
  };
  return flags[language] || '🌐';
};
