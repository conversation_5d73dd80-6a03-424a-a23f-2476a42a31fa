import { useState, useEffect } from 'react';
import { categoryApi, ICategory } from '@/services/categoryApi';
import { useI18n } from '@/hooks/useI18n';

interface UseCategoriesReturn {
  categories: ICategory[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useCategories = (): UseCategoriesReturn => {
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { language } = useI18n();

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await categoryApi.getRootCategories(language);
      
      if (response.success && response.data) {
        setCategories(response.data);
      } else {
        throw new Error('Failed to fetch categories');
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Fallback to static categories if API fails
      const fallbackCategories: ICategory[] = [
        {
          _id: 'argan-oil',
          name: {
            ar: 'زيت الأركان',
            fr: "Huile d'Argan",
            en: 'Argan Oil'
          },
          slug: {
            ar: 'زيت-الأركان',
            fr: 'huile-argan',
            en: 'argan-oil'
          },
          description: {
            ar: 'زيت الأركان الطبيعي من المغرب',
            fr: "Huile d'argan naturelle du Maroc",
            en: 'Natural argan oil from Morocco'
          },
          image: '/images/categories/argan-oil-category.jpg',
          isActive: true,
          sortOrder: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'honey',
          name: {
            ar: 'العسل',
            fr: 'Miel',
            en: 'Honey'
          },
          slug: {
            ar: 'العسل',
            fr: 'miel',
            en: 'honey'
          },
          description: {
            ar: 'عسل طبيعي من الجبال المغربية',
            fr: 'Miel naturel des montagnes marocaines',
            en: 'Natural honey from Moroccan mountains'
          },
          image: '/images/categories/honey-category.jpg',
          isActive: true,
          sortOrder: 2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          _id: 'amlou',
          name: {
            ar: 'أملو',
            fr: 'Amlou',
            en: 'Amlou'
          },
          slug: {
            ar: 'أملو',
            fr: 'amlou',
            en: 'amlou'
          },
          description: {
            ar: 'أملو تقليدي مغربي',
            fr: 'Amlou traditionnel marocain',
            en: 'Traditional Moroccan amlou'
          },
          image: '/images/categories/amlou-category.jpg',
          isActive: true,
          sortOrder: 3,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];
      
      setCategories(fallbackCategories);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [language]);

  const refetch = async () => {
    await fetchCategories();
  };

  return {
    categories,
    loading,
    error,
    refetch
  };
};

export default useCategories;
