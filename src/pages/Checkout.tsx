import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight, Check, CreditCard, User, MapPin, ShoppingBag } from 'lucide-react';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useCart } from '@/contexts/CartContext';
import { useI18n } from '@/hooks/useI18n';
import { toast } from 'sonner';
import { orderService, OrderData } from '@/services/orderService';

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  age: string;
  dateOfBirth: string;
}

interface DeliveryAddress {
  streetAddress: string;
  city: string;
  postalCode: string;
  country: string;
  region: string;
}

interface FormErrors {
  [key: string]: string;
}

const Checkout: React.FC = () => {
  const navigate = useNavigate();
  const { t, language, isRTL } = useI18n();
  const { state, clearCart } = useCart();
  const [currentStep, setCurrentStep] = useState(1);
  const [isProcessing, setIsProcessing] = useState(false);

  // Form data
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    age: '',
    dateOfBirth: ''
  });

  const [deliveryAddress, setDeliveryAddress] = useState<DeliveryAddress>({
    streetAddress: '',
    city: '',
    postalCode: '',
    country: 'Morocco',
    region: ''
  });

  const [paymentMethod, setPaymentMethod] = useState('cashOnDelivery');
  const [errors, setErrors] = useState<FormErrors>({});

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^(\+212|0)[5-7][0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validateStep1 = (): boolean => {
    const newErrors: FormErrors = {};

    if (!customerInfo.firstName.trim()) {
      newErrors.firstName = t('checkout.required');
    }
    if (!customerInfo.lastName.trim()) {
      newErrors.lastName = t('checkout.required');
    }
    if (!customerInfo.email.trim()) {
      newErrors.email = t('checkout.required');
    } else if (!validateEmail(customerInfo.email)) {
      newErrors.email = t('checkout.invalidEmail');
    }
    if (!customerInfo.phone.trim()) {
      newErrors.phone = t('checkout.required');
    } else if (!validatePhone(customerInfo.phone)) {
      newErrors.phone = t('checkout.invalidPhone');
    }
    if (!customerInfo.age.trim()) {
      newErrors.age = t('checkout.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = (): boolean => {
    const newErrors: FormErrors = {};

    if (!deliveryAddress.streetAddress.trim()) {
      newErrors.streetAddress = t('checkout.required');
    }
    if (!deliveryAddress.city.trim()) {
      newErrors.city = t('checkout.required');
    }
    if (!deliveryAddress.postalCode.trim()) {
      newErrors.postalCode = t('checkout.required');
    }
    if (!deliveryAddress.region.trim()) {
      newErrors.region = t('checkout.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (currentStep === 1 && !validateStep1()) return;
    if (currentStep === 2 && !validateStep2()) return;
    
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
      setErrors({});
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      setErrors({});
    }
  };

  const handlePlaceOrder = async () => {
    setIsProcessing(true);

    try {
      // Prepare order data
      const orderData: OrderData = {
        customerInfo,
        deliveryAddress,
        paymentMethod,
        items: state.items,
        subtotal: state.total,
        shippingFee,
        total: finalTotal
      };

      // Create order in database
      const result = await orderService.createOrder(orderData);

      if (result.success && result.orderId) {
        // Generate order confirmation data
        const confirmationData = orderService.generateOrderConfirmation(orderData, result.orderId);

        console.log('Order placed successfully:', confirmationData);

        // Clear cart and show success
        clearCart();
        toast.success(`${t('checkout.orderPlaced')} - ${result.orderId}`);

        // Navigate to success page or home
        navigate('/', { replace: true });
      } else {
        throw new Error(result.error || 'Failed to create order');
      }

    } catch (error) {
      console.error('Order placement failed:', error);
      toast.error(t('common.error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const shippingFee = state.total >= 500 ? 0 : 50;
  const finalTotal = state.total + shippingFee;

  const steps = [
    { number: 1, title: t('checkout.customerInfo'), icon: User },
    { number: 2, title: t('checkout.deliveryAddress'), icon: MapPin },
    { number: 3, title: t('checkout.paymentMethod'), icon: CreditCard },
    { number: 4, title: t('checkout.orderReview'), icon: ShoppingBag }
  ];

  // Redirect if cart is empty
  if (state.items.length === 0) {
    navigate('/cart');
    return null;
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-sand/20 via-white to-olive/10 py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-4xl mx-auto"
          >
            {/* Header */}
            <div className={`flex items-center gap-4 mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <Button
                variant="ghost"
                onClick={() => navigate('/cart')}
                className="p-2"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <h1 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                {t('checkout.title')}
              </h1>
            </div>

            {/* Progress Steps */}
            <div className="mb-8">
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                {steps.map((step, index) => {
                  const Icon = step.icon;
                  const isActive = currentStep === step.number;
                  const isCompleted = currentStep > step.number;
                  
                  return (
                    <div key={step.number} className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}>
                      <div className="flex flex-col items-center">
                        <div className={`
                          w-12 h-12 rounded-full flex items-center justify-center border-2 transition-colors
                          ${isActive ? 'bg-olive border-olive text-white' : 
                            isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                            'bg-white border-gray-300 text-gray-400'}
                        `}>
                          {isCompleted ? <Check className="h-5 w-5" /> : <Icon className="h-5 w-5" />}
                        </div>
                        <span className={`mt-2 text-sm font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'} ${
                          isActive ? 'text-olive' : isCompleted ? 'text-green-600' : 'text-gray-500'
                        }`}>
                          {step.title}
                        </span>
                      </div>
                      {index < steps.length - 1 && (
                        <div className={`flex-1 h-0.5 mx-4 ${
                          currentStep > step.number ? 'bg-green-500' : 'bg-gray-200'
                        }`} />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Form Steps */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className={`${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                      {t('checkout.step')} {currentStep} {t('checkout.of')} 4: {steps[currentStep - 1].title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <AnimatePresence mode="wait">
                      {/* Step 1: Customer Information */}
                      {currentStep === 1 && (
                        <motion.div
                          key="step1"
                          initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="firstName" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.firstName')} *
                              </Label>
                              <Input
                                id="firstName"
                                value={customerInfo.firstName}
                                onChange={(e) => setCustomerInfo({...customerInfo, firstName: e.target.value})}
                                className={errors.firstName ? 'border-red-500' : ''}
                                dir={isRTL ? 'rtl' : 'ltr'}
                              />
                              {errors.firstName && (
                                <p className="text-red-500 text-sm mt-1">{errors.firstName}</p>
                              )}
                            </div>
                            <div>
                              <Label htmlFor="lastName" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.lastName')} *
                              </Label>
                              <Input
                                id="lastName"
                                value={customerInfo.lastName}
                                onChange={(e) => setCustomerInfo({...customerInfo, lastName: e.target.value})}
                                className={errors.lastName ? 'border-red-500' : ''}
                                dir={isRTL ? 'rtl' : 'ltr'}
                              />
                              {errors.lastName && (
                                <p className="text-red-500 text-sm mt-1">{errors.lastName}</p>
                              )}
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="email" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                              {t('checkout.email')} *
                            </Label>
                            <Input
                              id="email"
                              type="email"
                              value={customerInfo.email}
                              onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                              className={errors.email ? 'border-red-500' : ''}
                              dir="ltr"
                            />
                            {errors.email && (
                              <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                            )}
                          </div>

                          <div>
                            <Label htmlFor="phone" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                              {t('checkout.phone')} *
                            </Label>
                            <Input
                              id="phone"
                              type="tel"
                              value={customerInfo.phone}
                              onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                              className={errors.phone ? 'border-red-500' : ''}
                              placeholder="+212 6XX XXX XXX"
                              dir="ltr"
                            />
                            {errors.phone && (
                              <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="age" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.age')} *
                              </Label>
                              <Input
                                id="age"
                                type="number"
                                value={customerInfo.age}
                                onChange={(e) => setCustomerInfo({...customerInfo, age: e.target.value})}
                                className={errors.age ? 'border-red-500' : ''}
                                min="18"
                                max="120"
                              />
                              {errors.age && (
                                <p className="text-red-500 text-sm mt-1">{errors.age}</p>
                              )}
                            </div>
                            <div>
                              <Label htmlFor="dateOfBirth" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.dateOfBirth')}
                              </Label>
                              <Input
                                id="dateOfBirth"
                                type="date"
                                value={customerInfo.dateOfBirth}
                                onChange={(e) => setCustomerInfo({...customerInfo, dateOfBirth: e.target.value})}
                              />
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {/* Step 2: Delivery Address */}
                      {currentStep === 2 && (
                        <motion.div
                          key="step2"
                          initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                          className="space-y-4"
                        >
                          <div>
                            <Label htmlFor="streetAddress" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                              {t('checkout.streetAddress')} *
                            </Label>
                            <Input
                              id="streetAddress"
                              value={deliveryAddress.streetAddress}
                              onChange={(e) => setDeliveryAddress({...deliveryAddress, streetAddress: e.target.value})}
                              className={errors.streetAddress ? 'border-red-500' : ''}
                              dir={isRTL ? 'rtl' : 'ltr'}
                            />
                            {errors.streetAddress && (
                              <p className="text-red-500 text-sm mt-1">{errors.streetAddress}</p>
                            )}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="city" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.city')} *
                              </Label>
                              <Input
                                id="city"
                                value={deliveryAddress.city}
                                onChange={(e) => setDeliveryAddress({...deliveryAddress, city: e.target.value})}
                                className={errors.city ? 'border-red-500' : ''}
                                dir={isRTL ? 'rtl' : 'ltr'}
                              />
                              {errors.city && (
                                <p className="text-red-500 text-sm mt-1">{errors.city}</p>
                              )}
                            </div>
                            <div>
                              <Label htmlFor="postalCode" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.postalCode')} *
                              </Label>
                              <Input
                                id="postalCode"
                                value={deliveryAddress.postalCode}
                                onChange={(e) => setDeliveryAddress({...deliveryAddress, postalCode: e.target.value})}
                                className={errors.postalCode ? 'border-red-500' : ''}
                                dir="ltr"
                              />
                              {errors.postalCode && (
                                <p className="text-red-500 text-sm mt-1">{errors.postalCode}</p>
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label htmlFor="country" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.country')} *
                              </Label>
                              <Input
                                id="country"
                                value={deliveryAddress.country}
                                onChange={(e) => setDeliveryAddress({...deliveryAddress, country: e.target.value})}
                                dir={isRTL ? 'rtl' : 'ltr'}
                                readOnly
                              />
                            </div>
                            <div>
                              <Label htmlFor="region" className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t('checkout.region')} *
                              </Label>
                              <Input
                                id="region"
                                value={deliveryAddress.region}
                                onChange={(e) => setDeliveryAddress({...deliveryAddress, region: e.target.value})}
                                className={errors.region ? 'border-red-500' : ''}
                                dir={isRTL ? 'rtl' : 'ltr'}
                              />
                              {errors.region && (
                                <p className="text-red-500 text-sm mt-1">{errors.region}</p>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {/* Step 3: Payment Method */}
                      {currentStep === 3 && (
                        <motion.div
                          key="step3"
                          initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                          className="space-y-4"
                        >
                          <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                            <div className="space-y-3">
                              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-gray-50">
                                <RadioGroupItem value="cashOnDelivery" id="cashOnDelivery" />
                                <Label htmlFor="cashOnDelivery" className={`flex-1 cursor-pointer ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                                  {t('checkout.cashOnDelivery')}
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-gray-50">
                                <RadioGroupItem value="creditCard" id="creditCard" />
                                <Label htmlFor="creditCard" className={`flex-1 cursor-pointer ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                                  {t('checkout.creditCard')}
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-gray-50">
                                <RadioGroupItem value="bankTransfer" id="bankTransfer" />
                                <Label htmlFor="bankTransfer" className={`flex-1 cursor-pointer ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                                  {t('checkout.bankTransfer')}
                                </Label>
                              </div>
                              <div className="flex items-center space-x-2 p-4 border rounded-lg hover:bg-gray-50">
                                <RadioGroupItem value="paypal" id="paypal" />
                                <Label htmlFor="paypal" className={`flex-1 cursor-pointer ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                                  {t('checkout.paypal')}
                                </Label>
                              </div>
                            </div>
                          </RadioGroup>
                        </motion.div>
                      )}

                      {/* Step 4: Order Review */}
                      {currentStep === 4 && (
                        <motion.div
                          key="step4"
                          initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                          className="space-y-6"
                        >
                          {/* Customer Info Review */}
                          <div>
                            <h3 className={`font-semibold mb-3 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                              {t('checkout.customerInfo')}
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                <strong>{t('checkout.firstName')}:</strong> {customerInfo.firstName}
                              </p>
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                <strong>{t('checkout.lastName')}:</strong> {customerInfo.lastName}
                              </p>
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                <strong>{t('checkout.email')}:</strong> {customerInfo.email}
                              </p>
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                <strong>{t('checkout.phone')}:</strong> {customerInfo.phone}
                              </p>
                            </div>
                          </div>

                          {/* Delivery Address Review */}
                          <div>
                            <h3 className={`font-semibold mb-3 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                              {t('checkout.deliveryAddress')}
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {deliveryAddress.streetAddress}<br />
                                {deliveryAddress.city}, {deliveryAddress.postalCode}<br />
                                {deliveryAddress.region}, {deliveryAddress.country}
                              </p>
                            </div>
                          </div>

                          {/* Payment Method Review */}
                          <div>
                            <h3 className={`font-semibold mb-3 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                              {t('checkout.paymentMethod')}
                            </h3>
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <p className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                                {t(`checkout.${paymentMethod}`)}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>

                    {/* Navigation Buttons */}
                    <div className={`flex justify-between mt-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Button
                        variant="outline"
                        onClick={handlePrevious}
                        disabled={currentStep === 1}
                      >
                        <ArrowLeft className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {t('checkout.previous')}
                      </Button>

                      {currentStep < 4 ? (
                        <Button onClick={handleNext} className="bg-olive hover:bg-olive/90">
                          {t('checkout.next')}
                          <ArrowRight className={`h-4 w-4 ${isRTL ? 'mr-2' : 'ml-2'}`} />
                        </Button>
                      ) : (
                        <Button 
                          onClick={handlePlaceOrder}
                          disabled={isProcessing}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {isProcessing ? t('checkout.processing') : t('checkout.placeOrder')}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <Card className="sticky top-4">
                  <CardHeader>
                    <CardTitle className={`${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                      {t('cart.orderSummary')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Cart Items */}
                    <div className="space-y-3">
                      {state.items.map((item) => (
                        <div key={`${item.id}-${JSON.stringify(item.attributes)}`} className={`flex gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-12 h-12 object-cover rounded"
                          />
                          <div className="flex-1 min-w-0">
                            <p className={`text-sm font-medium truncate ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                              {item.title}
                            </p>
                            <p className="text-sm text-gray-600">
                              {item.quantity} × {item.price.toFixed(2)} MAD
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Separator />

                    {/* Totals */}
                    <div className="space-y-2">
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                          {t('cart.subtotal')}
                        </span>
                        <span className="font-medium">{state.total.toFixed(2)} MAD</span>
                      </div>

                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                          {t('cart.shipping')}
                        </span>
                        <span className={`font-medium ${shippingFee === 0 ? 'text-green-600' : ''}`}>
                          {shippingFee === 0 ? t('cart.freeShipping') : `${shippingFee.toFixed(2)} MAD`}
                        </span>
                      </div>

                      <Separator />

                      <div className={`flex justify-between text-lg font-bold ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-title'}>
                          {t('cart.total')}
                        </span>
                        <span className="text-olive">{finalTotal.toFixed(2)} MAD</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default Checkout;
