
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Layout from '@/components/Layout';
import ProductDetail from '@/components/ProductDetail';
import { productApi } from '@/services/api';
import { useLanguage } from '@/contexts/LanguageContext';
import { motion } from 'framer-motion';

const ProductPage = () => {
  const { id } = useParams<{ id: string }>();
  const { language } = useLanguage();
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProduct = async () => {
      if (!id) {
        setError('No product ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get product by ID directly from API
        const response = await productApi.getProductById(id);

        if (response.success && response.data) {
          // Transform API product to frontend format
          const apiProduct = response.data;
          const transformedProduct = {
            id: apiProduct._id || apiProduct.id,
            title: apiProduct.title,
            price: apiProduct.price,
            originalPrice: apiProduct.originalPrice,
            discountPercentage: apiProduct.discountPercentage,
            category: apiProduct.category,
            image: (apiProduct.images && apiProduct.images.length > 0) ? apiProduct.images[0] : '/placeholder.svg',
            isOrganic: apiProduct.isOrganic,
            tags: apiProduct.tags,
            description: apiProduct.description,
            benefits: apiProduct.benefits ? (Array.isArray(apiProduct.benefits) ? apiProduct.benefits : apiProduct.benefits[language] || []) : [],
            extractionMethod: apiProduct.extractionMethod,
            viscosity: apiProduct.viscosity,
            producer: apiProduct.producer,
            inStock: apiProduct.inStock,
            stockQuantity: apiProduct.stockQuantity,
            weight: apiProduct.weight,
            volume: apiProduct.volume,
            ingredients: apiProduct.ingredients,
            usage: apiProduct.usage
          };

          setProduct(transformedProduct);
        } else {
          setError('Product not found');
        }
      } catch (err) {
        console.error('Failed to load product:', err);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    loadProduct();
  }, [id]);

  if (loading) {
    return (
      <Layout>
        <motion.div
          className="w-full px-4 sm:px-6 lg:px-8 py-16 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-golden mx-auto mb-4"></div>
          <p className="text-gray-600">
            {language === 'ar'
              ? 'جاري تحميل المنتج...'
              : language === 'fr'
                ? 'Chargement du produit...'
                : 'Loading product...'}
          </p>
        </motion.div>
      </Layout>
    );
  }

  if (error || !product) {
    return (
      <Layout>
        <motion.div
          className="w-full px-4 sm:px-6 lg:px-8 py-16 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl font-bold mb-4">
            {language === 'ar'
              ? 'المنتج غير موجود'
              : language === 'fr'
                ? 'Produit non trouvé'
                : 'Product Not Found'}
          </h1>
          <p className="text-gray-600 mb-6">
            {language === 'ar'
              ? 'المنتج الذي تبحث عنه غير موجود أو تمت إزالته.'
              : language === 'fr'
                ? 'Le produit que vous recherchez n\'existe pas ou a été supprimé.'
                : 'The product you\'re looking for doesn\'t exist or has been removed.'}
          </p>
          <motion.div className="flex flex-col sm:flex-row gap-4 justify-center">
            <motion.a
              href="/"
              className="inline-block bg-golden text-white px-6 py-3 rounded-lg hover:bg-golden/90 transition-colors text-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {language === 'ar'
                ? 'العودة إلى الصفحة الرئيسية'
                : language === 'fr'
                  ? 'Retour à l\'accueil'
                  : 'Back to Home'}
            </motion.a>
            <motion.button
              onClick={() => window.location.reload()}
              className="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {language === 'ar'
                ? 'إعادة المحاولة'
                : language === 'fr'
                  ? 'Réessayer'
                  : 'Try Again'}
            </motion.button>
          </motion.div>
        </motion.div>
      </Layout>
    );
  }

  return (
    <Layout>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.4 }}
      >
        <ProductDetail
          id={product.id}
          title={product.title[language as keyof typeof product.title]}
          price={product.price}
          originalPrice={product.discountPercentage ? product.originalPrice : undefined}
          image={product.image}
          category={product.category}
          isOrganic={product.isOrganic}
          discountPercentage={product.discountPercentage}
          benefits={product.benefits}
          description={product.description ? product.description[language as keyof typeof product.description] : undefined}
          extractionMethod={product.extractionMethod}
          viscosity={product.viscosity}
          producer={product.producer}
        />
      </motion.div>
    </Layout>
  );
};

export default ProductPage;
