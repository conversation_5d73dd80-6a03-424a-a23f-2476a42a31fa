
import React from 'react';
import { useParams } from 'react-router-dom';
import Layout from '@/components/Layout';
import ProductDetail from '@/components/ProductDetail';
import { getProductById } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';
import { motion } from 'framer-motion';

const ProductPage = () => {
  const { id } = useParams<{ id: string }>();
  const { language } = useLanguage();
  const product = getProductById(id || '');

  if (!product) {
    return (
      <Layout>
        <motion.div 
          className="container-custom py-16 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-2xl font-bold mb-4">
            {language === 'ar' 
              ? 'المنتج غير موجود' 
              : language === 'fr' 
                ? 'Produit non trouvé' 
                : 'Product Not Found'}
          </h1>
          <p>
            {language === 'ar' 
              ? 'المنتج الذي تبحث عنه غير موجود أو تمت إزالته.' 
              : language === 'fr' 
                ? 'Le produit que vous recherchez n\'existe pas ou a été supprimé.' 
                : 'The product you\'re looking for doesn\'t exist or has been removed.'}
          </p>
        </motion.div>
      </Layout>
    );
  }

  return (
    <Layout>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.4 }}
      >
        <ProductDetail
          id={product.id}
          title={product.title[language as keyof typeof product.title]}
          price={product.price}
          originalPrice={product.discountPercentage ? product.originalPrice : undefined}
          image={product.image}
          category={product.category}
          isOrganic={product.isOrganic}
          discountPercentage={product.discountPercentage}
          benefits={product.benefits}
          description={product.description ? product.description[language as keyof typeof product.description] : undefined}
          extractionMethod={product.extractionMethod}
          viscosity={product.viscosity}
          producer={product.producer}
        />
      </motion.div>
    </Layout>
  );
};

export default ProductPage;
