
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useI18n } from '@/hooks/useI18n';

const ArganOil = () => {
  const { language } = useI18n();
  const arganProducts = getProductsByCategory('argan');

  // Transform products to include localized titles and stock status
  const localizedProducts = arganProducts.map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
    inStock: Math.random() > 0.05, // Random stock status for demo
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="argan"
        titleKey="argan"
        filterOptions={['pure', 'cosmetic', 'culinary', 'roasted', 'unroasted']}
        products={localizedProducts}
      />
    </Layout>
  );
};

export default ArganOil;
