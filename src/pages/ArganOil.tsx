
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';

const ArganOil = () => {
  const { language } = useLanguage();
  const arganProducts = getProductsByCategory('argan');

  // Transform products to include localized titles
  const localizedProducts = arganProducts.map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title]
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="argan"
        titleKey="argan"
        products={localizedProducts}
      />
    </Layout>
  );
};

export default ArganOil;
