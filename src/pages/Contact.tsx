import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import { useI18n } from '@/hooks/useI18n';
import { useReducedMotion, getAnimationVariants, getHoverAnimation } from '@/hooks/useReducedMotion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { MapPin, Phone, Mail, Clock, Send } from 'lucide-react';

const Contact = () => {
  const { language, t, isRTL } = useI18n();
  const prefersReducedMotion = useReducedMotion();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const containerVariants = prefersReducedMotion ? {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  } : {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = getAnimationVariants(prefersReducedMotion);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // Reset form
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: MapPin,
      titleKey: 'contact.info.address.title',
      valueKey: 'contact.info.address.value',
      link: 'https://maps.google.com/?q=Imlil,+Morocco'
    },
    {
      icon: Phone,
      titleKey: 'contact.info.phone.title',
      valueKey: 'contact.info.phone.value',
      link: 'tel:+212123456789'
    },
    {
      icon: Mail,
      titleKey: 'contact.info.email.title',
      valueKey: 'contact.info.email.value',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: Clock,
      titleKey: 'contact.info.hours.title',
      valueKey: 'contact.info.hours.value'
    }
  ];

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-16"
      >
        {/* Hero Section */}
        <motion.div 
          variants={itemVariants}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <Mail className="text-olive h-8 w-8" />
            <h1 className={`text-4xl md:text-5xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('contact.title')}
            </h1>
          </div>
          <p className={`text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('contact.subtitle')}
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <motion.div variants={itemVariants}>
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className={`text-2xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
                {t('contact.form.title')}
              </h2>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <motion.div
                    {...getHoverAnimation(prefersReducedMotion)}
                  >
                    <label className={`block text-sm font-medium text-gray-700 mb-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {t('contact.form.name')}
                    </label>
                    <motion.div
                      animate={{
                        scale: focusedField === 'name' ? 1.02 : 1,
                        boxShadow: focusedField === 'name' ? '0 0 0 3px rgba(212, 175, 55, 0.1)' : '0 0 0 0px transparent'
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField('name')}
                        onBlur={() => setFocusedField(null)}
                        required
                        className={`${language === 'ar' ? 'font-arabic text-right' : 'font-body'} transition-all duration-200`}
                        placeholder={t('contact.form.namePlaceholder')}
                      />
                    </motion.div>
                  </motion.div>
                  <motion.div
                    {...getHoverAnimation(prefersReducedMotion)}
                  >
                    <label className={`block text-sm font-medium text-gray-700 mb-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {t('contact.form.email')}
                    </label>
                    <motion.div
                      animate={{
                        scale: focusedField === 'email' ? 1.02 : 1,
                        boxShadow: focusedField === 'email' ? '0 0 0 3px rgba(212, 175, 55, 0.1)' : '0 0 0 0px transparent'
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        onFocus={() => setFocusedField('email')}
                        onBlur={() => setFocusedField(null)}
                        required
                        className={`${language === 'ar' ? 'font-arabic text-right' : 'font-body'} transition-all duration-200`}
                        placeholder={t('contact.form.emailPlaceholder')}
                      />
                    </motion.div>
                  </motion.div>
                </div>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {t('contact.form.phone')}
                    </label>
                    <Input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className={`${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
                      placeholder={t('contact.form.phonePlaceholder')}
                    />
                  </div>
                  <div>
                    <label className={`block text-sm font-medium text-gray-700 mb-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {t('contact.form.subject')}
                    </label>
                    <Input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className={`${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
                      placeholder={t('contact.form.subjectPlaceholder')}
                    />
                  </div>
                </div>
                
                <div>
                  <label className={`block text-sm font-medium text-gray-700 mb-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {t('contact.form.message')}
                  </label>
                  <Textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className={`${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
                    placeholder={t('contact.form.messagePlaceholder')}
                  />
                </div>
                
                <motion.div
                  whileHover={prefersReducedMotion ? {} : {
                    scale: 1.02,
                    boxShadow: "0 10px 25px rgba(0,0,0,0.15)"
                  }}
                  whileTap={prefersReducedMotion ? {} : { scale: 0.98 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 20
                  }}
                >
                  <Button
                    type="submit"
                    className={`w-full bg-olive hover:bg-olive/90 transition-all duration-300 ${language === 'ar' ? 'font-arabic' : 'font-body'} group`}
                  >
                    <motion.div
                      animate={{ x: 0 }}
                      whileHover={prefersReducedMotion ? {} : { x: 5 }}
                      transition={{ duration: 0.2 }}
                      className="flex items-center justify-center"
                    >
                      <Send className="h-4 w-4 mr-2 group-hover:rotate-12 transition-transform duration-200" />
                      {t('contact.form.submit')}
                    </motion.div>
                  </Button>
                </motion.div>
              </form>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div variants={itemVariants} className="space-y-8">
            {/* Contact Details */}
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className={`text-2xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
                {t('contact.info.title')}
              </h2>
              
              <div className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-olive/10 rounded-full flex items-center justify-center flex-shrink-0">
                      <info.icon className="h-6 w-6 text-olive" />
                    </div>
                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                      <h3 className={`font-semibold text-gray-800 mb-1 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                        {t(info.titleKey)}
                      </h3>
                      {info.link ? (
                        <a 
                          href={info.link}
                          target={info.link.startsWith('http') ? '_blank' : undefined}
                          rel={info.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                          className={`text-gray-600 hover:text-olive transition-colors ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                        >
                          {t(info.valueKey)}
                        </a>
                      ) : (
                        <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                          {t(info.valueKey)}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Map Integration */}
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
              <div className="p-6 border-b">
                <h3 className={`text-xl font-semibold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
                  {t('contact.map.title')}
                </h3>
              </div>
              <div className="h-64 bg-gradient-to-br from-olive/20 to-sand/20 flex items-center justify-center">
                <Button
                  onClick={() => window.open('https://maps.google.com/?q=Imlil,+Morocco', '_blank')}
                  className={`bg-olive hover:bg-olive/90 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                >
                  <MapPin className="h-4 w-4 mr-2" />
                  {t('contact.map.openInMaps')}
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </Layout>
  );
};

export default Contact;
