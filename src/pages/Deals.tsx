
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useI18n } from '@/hooks/useI18n';

const Deals = () => {
  const { language } = useI18n();

  // Get all products with discounts from all categories
  const allProducts = [
    ...getProductsByCategory('honey'),
    ...getProductsByCategory('argan'),
    ...getProductsByCategory('amlou')
  ];

  const dealsProducts = allProducts
    .filter(product => product.discountPercentage && product.discountPercentage > 0)
    .map(product => ({
      ...product,
      title: product.title[language as keyof typeof product.title],
      originalPrice: product.originalPrice,
      inStock: Math.random() > 0.1, // Random stock status for demo
    }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="deals"
        titleKey="deals"
        products={dealsProducts}
        availableCategories={['honey', 'argan', 'amlou']}
      />
    </Layout>
  );
};

export default Deals;
