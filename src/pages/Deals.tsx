
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';

const Deals = () => {
  const { language } = useLanguage();
  const products = getProductsByCategory('deals').map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="deals"
        titleKey="deals"
        products={products}
      />
    </Layout>
  );
};

export default Deals;
