import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ShoppingCart, Trash2, Plus, Minus, ArrowLeft, CreditCard } from 'lucide-react';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useCart } from '@/contexts/CartContext';
import { useI18n } from '@/hooks/useI18n';
import { useCartToast } from '@/components/CartToast';
import { toast } from 'sonner';

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const { t, language, isRTL } = useI18n();
  const { state, updateQuantity, removeFromCart, clearCart } = useCart();
  const { showRemoveFromCartToast, showUpdateCartToast } = useCartToast();
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const handleQuantityUpdate = async (id: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setIsUpdating(id);
    const item = state.items.find(item => item.id === id);
    
    try {
      updateQuantity(id, newQuantity);
      if (item) {
        showUpdateCartToast(item.title, newQuantity);
      }
    } catch (error) {
      toast.error(t('common.error'));
    } finally {
      setTimeout(() => setIsUpdating(null), 300);
    }
  };

  const handleRemoveItem = (id: string) => {
    const item = state.items.find(item => item.id === id);
    removeFromCart(id);
    if (item) {
      showRemoveFromCartToast(item.title);
    }
  };

  const handleClearCart = () => {
    clearCart();
    toast.success(t('cart.clearCart'));
  };

  const handleCheckout = () => {
    navigate('/checkout');
  };

  const shippingFee = state.total >= 500 ? 0 : 50; // Free shipping over 500 MAD
  const finalTotal = state.total + shippingFee;

  if (state.items.length === 0) {
    return (
      <Layout>
        <div className="min-h-screen bg-gradient-to-br from-sand/20 via-white to-olive/10 py-12">
          <div className="container mx-auto px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-2xl mx-auto text-center"
            >
              <div className="bg-white rounded-2xl shadow-lg p-12">
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  className="mb-6"
                >
                  <ShoppingCart className="h-24 w-24 text-gray-300 mx-auto mb-4" />
                </motion.div>
                
                <h1 className={`text-3xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                  {t('cart.empty')}
                </h1>
                
                <p className={`text-gray-600 mb-8 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                  {t('cart.emptyDescription')}
                </p>
                
                <Button 
                  onClick={() => navigate('/')}
                  size="lg"
                  className="bg-olive hover:bg-olive/90"
                >
                  <ArrowLeft className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('cart.startShopping')}
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-sand/20 via-white to-olive/10 py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="max-w-6xl mx-auto"
          >
            {/* Header */}
            <div className={`flex items-center justify-between mb-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <Button
                  variant="ghost"
                  onClick={() => navigate(-1)}
                  className="p-2"
                >
                  <ArrowLeft className="h-5 w-5" />
                </Button>
                <h1 className={`text-3xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                  {t('cart.title')}
                </h1>
              </div>
              
              {state.items.length > 0 && (
                <Button
                  variant="outline"
                  onClick={handleClearCart}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Trash2 className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('cart.clearCart')}
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Cart Items */}
              <div className="lg:col-span-2">
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                  className="space-y-4"
                >
                  {state.items.map((item) => (
                    <motion.div
                      key={`${item.id}-${JSON.stringify(item.attributes)}`}
                      variants={itemVariants}
                    >
                      <Card className="overflow-hidden hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                          <div className={`flex gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                            {/* Product Image */}
                            <div className="w-24 h-24 flex-shrink-0">
                              <img
                                src={item.image}
                                alt={item.title}
                                className="w-full h-full object-cover rounded-lg"
                              />
                            </div>

                            {/* Product Details */}
                            <div className="flex-1 min-w-0">
                              <div className={`flex justify-between items-start mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <h3 className={`font-semibold text-lg truncate ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                                  {item.title}
                                </h3>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveItem(item.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50 p-1"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>

                              {/* Price */}
                              <div className={`flex items-center gap-2 mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <span className="text-xl font-bold text-olive">
                                  {item.price.toFixed(2)} MAD
                                </span>
                                {item.originalPrice && (
                                  <span className="text-sm text-gray-500 line-through">
                                    {item.originalPrice.toFixed(2)} MAD
                                  </span>
                                )}
                              </div>

                              {/* Quantity Controls */}
                              <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <span className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                                  {t('cart.quantity')}:
                                </span>
                                <div className="flex items-center border rounded-lg">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleQuantityUpdate(item.id, item.quantity - 1)}
                                    disabled={item.quantity <= 1 || isUpdating === item.id}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Minus className="h-3 w-3" />
                                  </Button>
                                  <span className="px-3 py-1 min-w-[3rem] text-center font-medium">
                                    {item.quantity}
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleQuantityUpdate(item.id, item.quantity + 1)}
                                    disabled={isUpdating === item.id}
                                    className="h-8 w-8 p-0"
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                </div>
                                <span className="text-sm text-gray-600 font-medium">
                                  = {(item.price * item.quantity).toFixed(2)} MAD
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </motion.div>
              </div>

              {/* Order Summary */}
              <div className="lg:col-span-1">
                <motion.div
                  initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <Card className="sticky top-4">
                    <CardHeader>
                      <CardTitle className={`${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                        {t('cart.orderSummary')}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                          {t('cart.subtotal')}
                        </span>
                        <span className="font-medium">{state.total.toFixed(2)} MAD</span>
                      </div>

                      <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                          {t('cart.shipping')}
                        </span>
                        <span className={`font-medium ${shippingFee === 0 ? 'text-green-600' : ''}`}>
                          {shippingFee === 0 ? t('cart.freeShipping') : `${shippingFee.toFixed(2)} MAD`}
                        </span>
                      </div>

                      <Separator />

                      <div className={`flex justify-between text-lg font-bold ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <span className={language === 'ar' ? 'font-arabic' : 'font-title'}>
                          {t('cart.total')}
                        </span>
                        <span className="text-olive">{finalTotal.toFixed(2)} MAD</span>
                      </div>

                      <div className="space-y-3 pt-4">
                        <Button
                          onClick={handleCheckout}
                          className="w-full bg-olive hover:bg-olive/90"
                          size="lg"
                        >
                          <CreditCard className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                          {t('cart.proceedToCheckout')}
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => navigate('/')}
                          className="w-full"
                        >
                          {t('cart.continueShopping')}
                        </Button>
                      </div>

                      {/* Free Shipping Notice */}
                      {state.total < 500 && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                          <p className={`text-sm text-blue-700 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                            {language === 'ar'
                              ? `أضف ${(500 - state.total).toFixed(2)} MAD للحصول على شحن مجاني`
                              : language === 'fr'
                              ? `Ajoutez ${(500 - state.total).toFixed(2)} MAD pour la livraison gratuite`
                              : `Add ${(500 - state.total).toFixed(2)} MAD for free shipping`
                            }
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default Cart;
