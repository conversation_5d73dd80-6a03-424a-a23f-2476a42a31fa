
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useI18n } from '@/hooks/useI18n';

const Honey = () => {
  const { language } = useI18n();
  const products = getProductsByCategory('honey').map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
    inStock: Math.random() > 0.05, // Random stock status for demo
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="honey"
        titleKey="honey"
        filterOptions={['thyme', 'wildflower', 'orange']}
        products={products}
      />
    </Layout>
  );
};

export default Honey;
