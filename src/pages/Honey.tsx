
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';

const Honey = () => {
  const { language } = useLanguage();
  const products = getProductsByCategory('honey').map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="honey"
        titleKey="honey"
        filterOptions={['thyme', 'wildflower', 'orange']}
        products={products}
      />
    </Layout>
  );
};

export default Honey;
