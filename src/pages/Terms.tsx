import React from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import { useI18n } from '@/hooks/useI18n';
import { FileText, Scale, ShoppingCart, RefreshCw, AlertTriangle, Phone } from 'lucide-react';

const Terms = () => {
  const { language, t, isRTL } = useI18n();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const sections = [
    {
      icon: Scale,
      titleKey: 'terms.sections.acceptance.title',
      contentKey: 'terms.sections.acceptance.content'
    },
    {
      icon: ShoppingCart,
      titleKey: 'terms.sections.products.title',
      contentKey: 'terms.sections.products.content'
    },
    {
      icon: RefreshCw,
      titleKey: 'terms.sections.orders.title',
      contentKey: 'terms.sections.orders.content'
    },
    {
      icon: AlertTriangle,
      titleKey: 'terms.sections.liability.title',
      contentKey: 'terms.sections.liability.content'
    },
    {
      icon: FileText,
      titleKey: 'terms.sections.intellectual.title',
      contentKey: 'terms.sections.intellectual.content'
    },
    {
      icon: Phone,
      titleKey: 'terms.sections.contact.title',
      contentKey: 'terms.sections.contact.content'
    }
  ];

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-16"
      >
        {/* Hero Section */}
        <motion.div 
          variants={itemVariants}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <Scale className="text-olive h-8 w-8" />
            <h1 className={`text-4xl md:text-5xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('terms.title')}
            </h1>
          </div>
          <p className={`text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('terms.subtitle')}
          </p>
          <div className={`mt-6 text-sm text-gray-500 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('terms.lastUpdated')}: {new Date().toLocaleDateString(language === 'ar' ? 'ar-MA' : language === 'fr' ? 'fr-FR' : 'en-US')}
          </div>
        </motion.div>

        {/* Introduction */}
        <motion.div 
          variants={itemVariants}
          className="bg-gradient-to-r from-olive/10 to-sand/10 rounded-2xl p-8 mb-12"
        >
          <h2 className={`text-2xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
            {t('terms.introduction.title')}
          </h2>
          <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'} ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('terms.introduction.content')}
          </p>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-olive/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <section.icon className="h-6 w-6 text-olive" />
                </div>
                <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <h3 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800`}>
                    {t(section.titleKey)}
                  </h3>
                  <div className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {t(section.contentKey)}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Important Notice */}
        <motion.div 
          variants={itemVariants}
          className="mt-16 bg-yellow-50 border border-yellow-200 rounded-2xl p-8"
        >
          <div className="flex items-start gap-4">
            <AlertTriangle className="h-8 w-8 text-yellow-600 flex-shrink-0 mt-1" />
            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
              <h2 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-yellow-800`}>
                {t('terms.notice.title')}
              </h2>
              <p className={`text-yellow-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                {t('terms.notice.content')}
              </p>
            </div>
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div 
          variants={itemVariants}
          className="mt-16 bg-olive/5 rounded-2xl p-8 text-center"
        >
          <h2 className={`text-2xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
            {t('terms.contact.title')}
          </h2>
          <p className={`text-gray-700 mb-6 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('terms.contact.description')}
          </p>
          <div className={`space-y-2 text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            <p>
              <strong>{t('terms.contact.email')}:</strong> <EMAIL>
            </p>
            <p>
              <strong>{t('terms.contact.address')}:</strong> {t('terms.contact.addressValue')}
            </p>
            <p>
              <strong>{t('terms.contact.phone')}:</strong> +212 123 456 789
            </p>
          </div>
        </motion.div>

        {/* Legal Notice */}
        <motion.div 
          variants={itemVariants}
          className="mt-12 text-center"
        >
          <div className={`text-sm text-gray-500 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            <p className="mb-2">
              {t('terms.legal.notice')}
            </p>
            <p>
              {t('terms.legal.jurisdiction')}
            </p>
          </div>
        </motion.div>
      </motion.div>
    </Layout>
  );
};

export default Terms;
