import React from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import { useI18n } from '@/hooks/useI18n';
import { Shield, Lock, Eye, UserCheck, FileText, Calendar } from 'lucide-react';

const Privacy = () => {
  const { language, t, isRTL } = useI18n();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const sections = [
    {
      icon: FileText,
      titleKey: 'privacy.sections.collection.title',
      contentKey: 'privacy.sections.collection.content'
    },
    {
      icon: Lock,
      titleKey: 'privacy.sections.usage.title',
      contentKey: 'privacy.sections.usage.content'
    },
    {
      icon: Shield,
      titleKey: 'privacy.sections.protection.title',
      contentKey: 'privacy.sections.protection.content'
    },
    {
      icon: Eye,
      titleKey: 'privacy.sections.sharing.title',
      contentKey: 'privacy.sections.sharing.content'
    },
    {
      icon: User<PERSON><PERSON><PERSON>,
      title<PERSON>ey: 'privacy.sections.rights.title',
      contentKey: 'privacy.sections.rights.content'
    },
    {
      icon: Calendar,
      titleKey: 'privacy.sections.updates.title',
      contentKey: 'privacy.sections.updates.content'
    }
  ];

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-16"
      >
        {/* Hero Section */}
        <motion.div 
          variants={itemVariants}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <Shield className="text-olive h-8 w-8" />
            <h1 className={`text-4xl md:text-5xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('privacy.title')}
            </h1>
          </div>
          <p className={`text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('privacy.subtitle')}
          </p>
          <div className={`mt-6 text-sm text-gray-500 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('privacy.lastUpdated')}: {new Date().toLocaleDateString(language === 'ar' ? 'ar-MA' : language === 'fr' ? 'fr-FR' : 'en-US')}
          </div>
        </motion.div>

        {/* Introduction */}
        <motion.div 
          variants={itemVariants}
          className="bg-gradient-to-r from-olive/10 to-sand/10 rounded-2xl p-8 mb-12"
        >
          <h2 className={`text-2xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
            {t('privacy.introduction.title')}
          </h2>
          <p className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'} ${isRTL ? 'text-right' : 'text-left'}`}>
            {t('privacy.introduction.content')}
          </p>
        </motion.div>

        {/* Privacy Sections */}
        <div className="space-y-8">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-olive/10 rounded-full flex items-center justify-center flex-shrink-0">
                  <section.icon className="h-6 w-6 text-olive" />
                </div>
                <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <h3 className={`text-xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800`}>
                    {t(section.titleKey)}
                  </h3>
                  <div className={`text-gray-700 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {t(section.contentKey)}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Contact Information */}
        <motion.div 
          variants={itemVariants}
          className="mt-16 bg-olive/5 rounded-2xl p-8 text-center"
        >
          <h2 className={`text-2xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
            {t('privacy.contact.title')}
          </h2>
          <p className={`text-gray-700 mb-6 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('privacy.contact.description')}
          </p>
          <div className={`space-y-2 text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            <p>
              <strong>{t('privacy.contact.email')}:</strong> <EMAIL>
            </p>
            <p>
              <strong>{t('privacy.contact.address')}:</strong> {t('privacy.contact.addressValue')}
            </p>
            <p>
              <strong>{t('privacy.contact.phone')}:</strong> +212 123 456 789
            </p>
          </div>
        </motion.div>

        {/* Legal Notice */}
        <motion.div 
          variants={itemVariants}
          className="mt-12 text-center"
        >
          <div className={`text-sm text-gray-500 leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            <p className="mb-2">
              {t('privacy.legal.notice')}
            </p>
            <p>
              {t('privacy.legal.compliance')}
            </p>
          </div>
        </motion.div>
      </motion.div>
    </Layout>
  );
};

export default Privacy;
