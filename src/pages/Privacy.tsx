import React from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import { useI18n } from '@/hooks/useI18n';
import { Shield, Lock, Eye, UserCheck, FileText, Calendar, Mail, MapPin, Phone, Clock, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

const Privacy = () => {
  const { language, t, isRTL } = useI18n();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const sections = [
    {
      icon: FileText,
      titleKey: 'privacy.sections.collection.title',
      contentKey: 'privacy.sections.collection.content'
    },
    {
      icon: Lock,
      titleKey: 'privacy.sections.usage.title',
      contentKey: 'privacy.sections.usage.content'
    },
    {
      icon: Shield,
      titleKey: 'privacy.sections.protection.title',
      contentKey: 'privacy.sections.protection.content'
    },
    {
      icon: Eye,
      titleKey: 'privacy.sections.sharing.title',
      contentKey: 'privacy.sections.sharing.content'
    },
    {
      icon: UserCheck,
      titleKey: 'privacy.sections.rights.title',
      contentKey: 'privacy.sections.rights.content'
    },
    {
      icon: Calendar,
      titleKey: 'privacy.sections.updates.title',
      contentKey: 'privacy.sections.updates.content'
    }
  ];

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-16"
      >
        {/* Hero Section */}
        <motion.div
          variants={itemVariants}
          className="relative text-center mb-20"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-olive/5 via-sand/5 to-olive/10 rounded-3xl -z-10"></div>
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,119,198,0.1),transparent_50%)] rounded-3xl -z-10"></div>

          <div className="relative py-16 px-8">
            {/* Icon and Badge */}
            <motion.div
              className="flex items-center justify-center gap-4 mb-8"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-olive/20 rounded-full blur-xl"></div>
                <div className="relative p-4 bg-gradient-to-br from-olive to-olive/80 rounded-full shadow-lg">
                  <Shield className="text-white h-8 w-8" />
                </div>
              </div>
              <Badge variant="secondary" className="bg-olive/10 text-olive border-olive/20 px-4 py-2 text-sm font-medium">
                <CheckCircle className="w-4 h-4 mr-2" />
                {t('privacy.badge', 'GDPR Compliant')}
              </Badge>
            </motion.div>

            {/* Title */}
            <motion.h1
              className={`text-5xl md:text-6xl lg:text-7xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} bg-gradient-to-r from-olive via-olive/80 to-sand bg-clip-text text-transparent`}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {t('privacy.title')}
            </motion.h1>

            {/* Subtitle */}
            <motion.p
              className={`text-xl md:text-2xl text-gray-700 max-w-4xl mx-auto leading-relaxed mb-8 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              {t('privacy.subtitle')}
            </motion.p>

            {/* Last Updated */}
            <motion.div
              className="flex items-center justify-center gap-2 text-gray-500"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <Clock className="w-4 h-4" />
              <span className={`text-sm ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                {t('privacy.lastUpdated')}: {new Date().toLocaleDateString(language === 'ar' ? 'ar-MA' : language === 'fr' ? 'fr-FR' : 'en-US')}
              </span>
            </motion.div>
          </div>
        </motion.div>

        {/* Introduction */}
        <motion.div
          variants={itemVariants}
          className="mb-16"
        >
          <Card className="border-0 shadow-xl bg-gradient-to-br from-white via-gray-50/50 to-olive/5">
            <CardContent className="p-10">
              <div className="flex items-start gap-6">
                <div className="p-3 bg-blue-100 rounded-xl">
                  <Info className="h-6 w-6 text-blue-600" />
                </div>
                <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <h2 className={`text-3xl font-bold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800`}>
                    {t('privacy.introduction.title')}
                  </h2>
                  <div className={`prose prose-lg max-w-none ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    <p className="text-gray-700 leading-relaxed text-lg mb-4">
                      {t('privacy.introduction.content')}
                    </p>
                    <div className="bg-olive/5 border-l-4 border-olive p-4 rounded-r-lg mt-6">
                      <p className="text-olive font-medium text-base mb-2">
                        {t('privacy.introduction.commitment', 'Our Commitment')}
                      </p>
                      <p className="text-gray-700 text-sm leading-relaxed">
                        {t('privacy.introduction.commitmentText', 'We are committed to maintaining the highest standards of data protection and privacy in accordance with international best practices and local regulations.')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Privacy Sections */}
        <div className="grid gap-8 lg:gap-12">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="group"
            >
              <Card className="border-0 shadow-lg hover:shadow-2xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/30 group-hover:from-olive/5 group-hover:to-sand/5">
                <CardContent className="p-8 lg:p-10">
                  <div className="flex items-start gap-6">
                    {/* Enhanced Icon */}
                    <motion.div
                      className="relative flex-shrink-0"
                      whileHover={{ scale: 1.1 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-br from-olive/20 to-sand/20 rounded-2xl blur-lg group-hover:blur-xl transition-all"></div>
                      <div className="relative w-16 h-16 bg-gradient-to-br from-olive to-olive/80 rounded-2xl flex items-center justify-center shadow-lg">
                        <section.icon className="h-8 w-8 text-white" />
                      </div>
                    </motion.div>

                    {/* Content */}
                    <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                      <div className="flex items-center gap-3 mb-6">
                        <h3 className={`text-2xl lg:text-3xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800 group-hover:text-olive transition-colors`}>
                          {t(section.titleKey)}
                        </h3>
                        <Badge variant="outline" className="text-xs px-2 py-1 border-olive/20 text-olive/70">
                          {String(index + 1).padStart(2, '0')}
                        </Badge>
                      </div>

                      <Separator className="mb-6 bg-gradient-to-r from-olive/20 via-sand/20 to-transparent" />

                      <div className={`prose prose-lg max-w-none ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                        <p className="text-gray-700 leading-relaxed text-lg">
                          {t(section.contentKey)}
                        </p>

                        {/* Additional enhanced content based on section */}
                        {index === 0 && (
                          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="text-blue-800 font-medium text-sm mb-1">
                                  {t('privacy.sections.collection.note', 'Important Note')}
                                </p>
                                <p className="text-blue-700 text-sm">
                                  {t('privacy.sections.collection.noteText', 'We only collect information that is necessary for providing our services and improving your experience.')}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}

                        {index === 2 && (
                          <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
                            <div className="flex items-start gap-3">
                              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                              <div>
                                <p className="text-green-800 font-medium text-sm mb-1">
                                  {t('privacy.sections.protection.security', 'Security Measures')}
                                </p>
                                <p className="text-green-700 text-sm">
                                  {t('privacy.sections.protection.securityText', 'We use industry-standard encryption and security protocols to protect your data.')}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Contact Information */}
        <motion.div
          variants={itemVariants}
          className="mt-20"
        >
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-olive/10 via-sand/5 to-olive/5">
            <CardContent className="p-10 lg:p-12">
              <div className="text-center mb-10">
                <div className="flex items-center justify-center gap-3 mb-6">
                  <div className="p-3 bg-olive/20 rounded-full">
                    <Mail className="h-6 w-6 text-olive" />
                  </div>
                  <h2 className={`text-3xl lg:text-4xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
                    {t('privacy.contact.title')}
                  </h2>
                </div>
                <p className={`text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                  {t('privacy.contact.description')}
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                {/* Email */}
                <motion.div
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Mail className="h-5 w-5 text-blue-600" />
                    </div>
                    <h3 className={`font-semibold text-gray-800 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                      {t('privacy.contact.email')}
                    </h3>
                  </div>
                  <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    <EMAIL>
                  </p>
                </motion.div>

                {/* Address */}
                <motion.div
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300, delay: 0.1 }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <MapPin className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className={`font-semibold text-gray-800 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                      {t('privacy.contact.address')}
                    </h3>
                  </div>
                  <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {t('privacy.contact.addressValue')}
                  </p>
                </motion.div>

                {/* Phone */}
                <motion.div
                  className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 300, delay: 0.2 }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Phone className="h-5 w-5 text-orange-600" />
                    </div>
                    <h3 className={`font-semibold text-gray-800 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                      {t('privacy.contact.phone')}
                    </h3>
                  </div>
                  <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    +212 123 456 789
                  </p>
                </motion.div>
              </div>

              {/* Response Time Notice */}
              <div className="mt-8 bg-olive/10 border border-olive/20 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-olive" />
                  <p className={`text-olive font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {t('privacy.contact.responseTime', 'We typically respond to privacy inquiries within 48 hours.')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Legal Notice */}
        <motion.div
          variants={itemVariants}
          className="mt-16"
        >
          <Card className="border border-gray-200 bg-gray-50/50">
            <CardContent className="p-8">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-6">
                  <FileText className="h-5 w-5 text-gray-600" />
                  <h3 className={`text-lg font-semibold text-gray-800 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                    {t('privacy.legal.title', 'Legal Notice')}
                  </h3>
                </div>

                <div className={`space-y-4 text-sm text-gray-600 leading-relaxed max-w-4xl mx-auto ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                  <p className="bg-white rounded-lg p-4 border border-gray-200">
                    {t('privacy.legal.notice')}
                  </p>
                  <p className="bg-white rounded-lg p-4 border border-gray-200">
                    {t('privacy.legal.compliance')}
                  </p>

                  {/* Additional Legal Information */}
                  <div className="grid md:grid-cols-2 gap-4 mt-6">
                    <div className="bg-white rounded-lg p-4 border border-gray-200">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-gray-800">
                          {t('privacy.legal.gdpr', 'GDPR Compliant')}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600">
                        {t('privacy.legal.gdprText', 'Fully compliant with EU General Data Protection Regulation')}
                      </p>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-gray-200">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-gray-800">
                          {t('privacy.legal.security', 'Data Security')}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600">
                        {t('privacy.legal.securityText', 'Industry-standard security measures implemented')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </Layout>
  );
};

export default Privacy;
