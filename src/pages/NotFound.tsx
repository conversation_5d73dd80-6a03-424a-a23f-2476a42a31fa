
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import Layout from "@/components/Layout";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/contexts/LanguageContext";

const NotFound = () => {
  const location = useLocation();
  const { language, isRTL } = useLanguage();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <Layout>
      <div className={`min-h-[60vh] flex items-center justify-center bg-muted ${isRTL ? 'text-right' : 'text-left'}`}>
        <div className="text-center p-8">
          <h1 className={`text-5xl font-bold mb-4 text-olive ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
            404
          </h1>
          <p className={`text-xl text-gray-600 mb-6 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {language === 'ar' 
              ? 'عذراً، الصفحة غير موجودة'
              : language === 'fr'
                ? 'Désolé, la page n\'existe pas'
                : 'Sorry, the page does not exist'}
          </p>
          <Button 
            asChild
            className={`bg-olive hover:bg-sand ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
          >
            <a href="/">
              {language === 'ar' 
                ? 'العودة إلى الصفحة الرئيسية'
                : language === 'fr'
                  ? 'Retour à l\'accueil'
                  : 'Return to Home'}
            </a>
          </Button>
        </div>
      </div>
    </Layout>
  );
};

export default NotFound;
