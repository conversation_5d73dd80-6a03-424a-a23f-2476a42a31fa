
import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/services/productService';
import { useI18n } from '@/hooks/useI18n';

const Amlou = () => {
  const { language } = useI18n();
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true);
        const amlouProducts = await getProductsByCategory('amlou');

        // Transform products to include localized titles and stock status
        const localizedProducts = amlouProducts.map(product => ({
          ...product,
          title: product.title[language as keyof typeof product.title],
          originalPrice: product.discountPercentage ? product.originalPrice : undefined,
          inStock: product.inStock !== undefined ? product.inStock : true,
        }));

        setProducts(localizedProducts);
        setError(null);
      } catch (err) {
        console.error('Failed to load amlou products:', err);
        setError('Failed to load products');
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, [language]);

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">Loading products...</div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-red-600">{error}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="amlou"
        titleKey="amlou"
        filterOptions={['traditional', 'honey', 'dates', 'almonds', 'walnuts']}
        products={products}
      />
    </Layout>
  );
};

export default Amlou;
