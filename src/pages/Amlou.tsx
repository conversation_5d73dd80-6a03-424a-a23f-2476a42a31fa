
import React from 'react';
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';

const Amlou = () => {
  const { language } = useLanguage();
  const products = getProductsByCategory('amlou').map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
  }));

  return (
    <Layout>
      <ProductCategoryLayout
        categoryId="amlou"
        titleKey="amlou"
        products={products}
      />
    </Layout>
  );
};

export default Amlou;
