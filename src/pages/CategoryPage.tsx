import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useI18n } from '@/hooks/useI18n';
import { categoryApi, ICategory } from '@/services/categoryApi';
import { productApi, Product } from '@/services/api';
import ProductCard from '@/components/ProductCard';
import { motion } from 'framer-motion';
import { Loader2, AlertCircle } from 'lucide-react';

const CategoryPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [searchParams] = useSearchParams();
  const { language, t, isRTL } = useI18n();
  
  const [category, setCategory] = useState<ICategory | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategoryAndProducts = async () => {
      if (!slug) return;

      try {
        setLoading(true);
        setError(null);

        // First, get all categories to find the one with matching slug
        const categoriesResponse = await categoryApi.getCategories({
          isActive: true,
          language
        });

        if (!categoriesResponse.success || !categoriesResponse.data) {
          throw new Error('Failed to fetch categories');
        }

        // Find category by slug
        const foundCategory = categoriesResponse.data.find(cat => 
          cat.slug[language] === slug
        );

        if (!foundCategory) {
          throw new Error('Category not found');
        }

        setCategory(foundCategory);

        // Get products for this category
        const productsResponse = await productApi.getProducts({
          category: foundCategory._id,
          page: 1,
          limit: 20
        });

        if (productsResponse.success && productsResponse.data) {
          setProducts(productsResponse.data);
        }

      } catch (err) {
        console.error('Error fetching category data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryAndProducts();
  }, [slug, language]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-olive" />
          <p className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-600`}>
            {t('loading')}
          </p>
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className={`text-2xl font-bold mb-2 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
            {t('category.notFound')}
          </h1>
          <p className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-600 mb-4`}>
            {error || t('category.notFoundDescription')}
          </p>
          <a 
            href="/"
            className="inline-flex items-center px-4 py-2 bg-olive text-white rounded-md hover:bg-dark-olive transition-colors"
          >
            {t('backToHome')}
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Category Header */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-white py-16"
      >
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            {category.image && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="mb-8"
              >
                <img
                  src={category.image}
                  alt={category.name[language]}
                  className="w-32 h-32 object-cover rounded-full mx-auto shadow-lg"
                />
              </motion.div>
            )}
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className={`text-4xl font-bold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-900`}
            >
              {category.name[language]}
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className={`text-lg text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
            >
              {category.description[language]}
            </motion.p>
          </div>
        </div>
      </motion.section>

      {/* Products Section */}
      <section className="py-16">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6 }}
            className="mb-8"
          >
            <h2 className={`text-2xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-900 mb-2`}>
              {t('category.products')}
            </h2>
            <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
              {products.length} {t('category.productsCount')}
            </p>
          </motion.div>

          {products.length > 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
              {products.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + index * 0.1, duration: 0.6 }}
                >
                  <ProductCard product={product} />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.6 }}
              className="text-center py-16"
            >
              <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic' : 'font-body'} text-lg`}>
                {t('category.noProducts')}
              </p>
            </motion.div>
          )}
        </div>
      </section>
    </div>
  );
};

export default CategoryPage;
