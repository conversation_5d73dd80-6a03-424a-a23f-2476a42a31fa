import React from 'react';
import { motion } from 'framer-motion';
import Layout from '@/components/Layout';
import { useI18n } from '@/hooks/useI18n';
import { Leaf, Heart, Award, Users, Mountain, Sparkles } from 'lucide-react';

const About = () => {
  const { language, t, isRTL } = useI18n();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const values = [
    {
      icon: Leaf,
      titleKey: 'about.values.natural.title',
      descKey: 'about.values.natural.description'
    },
    {
      icon: Heart,
      titleKey: 'about.values.quality.title',
      descKey: 'about.values.quality.description'
    },
    {
      icon: Award,
      titleKey: 'about.values.authentic.title',
      descKey: 'about.values.authentic.description'
    },
    {
      icon: Users,
      titleKey: 'about.values.community.title',
      descKey: 'about.values.community.description'
    }
  ];

  const team = [
    {
      name: language === 'ar' ? 'أحمد الإدريسي' : language === 'fr' ? '<PERSON>' : '<PERSON>i',
      role: language === 'ar' ? 'المؤسس والمدير التنفيذي' : language === 'fr' ? 'Fondateur & PDG' : 'Founder & CEO',
      image: '/team/ahmed.jpg'
    },
    {
      name: language === 'ar' ? 'فاطمة الزهراء' : language === 'fr' ? 'Fatima Zahra' : 'Fatima Zahra',
      role: language === 'ar' ? 'مديرة الجودة' : language === 'fr' ? 'Directrice Qualité' : 'Quality Director',
      image: '/team/fatima.jpg'
    },
    {
      name: language === 'ar' ? 'يوسف الأمازيغي' : language === 'fr' ? 'Youssef Amazigh' : 'Youssef Amazigh',
      role: language === 'ar' ? 'خبير المنتجات الطبيعية' : language === 'fr' ? 'Expert Produits Naturels' : 'Natural Products Expert',
      image: '/team/youssef.jpg'
    }
  ];

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-16"
      >
        {/* Hero Section */}
        <motion.div 
          variants={itemVariants}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <div className="flex items-center justify-center gap-3 mb-6">
            <Mountain className="text-olive h-8 w-8" />
            <h1 className={`text-4xl md:text-5xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('about.title')}
            </h1>
          </div>
          <p className={`text-lg md:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('about.subtitle')}
          </p>
        </motion.div>

        {/* Story Section */}
        <motion.div 
          variants={itemVariants}
          className="grid md:grid-cols-2 gap-12 items-center mb-20"
        >
          <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
            <h2 className={`text-3xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('about.story.title')}
            </h2>
            <div className={`space-y-4 text-gray-700 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
              <p className="leading-relaxed">
                {t('about.story.paragraph1')}
              </p>
              <p className="leading-relaxed">
                {t('about.story.paragraph2')}
              </p>
              <p className="leading-relaxed">
                {t('about.story.paragraph3')}
              </p>
            </div>
          </div>
          <div className="relative">
            <img 
              src="/images/about/imlil-mountains.jpg" 
              alt="Imlil Mountains"
              className="rounded-2xl shadow-2xl w-full h-96 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-olive/20 to-transparent rounded-2xl"></div>
          </div>
        </motion.div>

        {/* Values Section */}
        <motion.div variants={itemVariants} className="mb-20">
          <div className={`text-center mb-12 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h2 className={`text-3xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('about.values.title')}
            </h2>
            <p className={`text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
              {t('about.values.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="w-16 h-16 bg-olive/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <value.icon className="h-8 w-8 text-olive" />
                </div>
                <h3 className={`text-xl font-semibold mb-3 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800`}>
                  {t(value.titleKey)}
                </h3>
                <p className={`text-gray-600 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                  {t(value.descKey)}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Team Section */}
        <motion.div variants={itemVariants} className="mb-20">
          <div className={`text-center mb-12 ${isRTL ? 'text-right' : 'text-left'}`}>
            <h2 className={`text-3xl font-semibold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('about.team.title')}
            </h2>
            <p className={`text-gray-600 max-w-2xl mx-auto ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
              {t('about.team.subtitle')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="h-64 bg-gradient-to-br from-olive/20 to-sand/20 flex items-center justify-center">
                  <Users className="h-24 w-24 text-olive/40" />
                </div>
                <div className="p-6">
                  <h3 className={`text-xl font-semibold mb-2 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-gray-800`}>
                    {member.name}
                  </h3>
                  <p className={`text-olive font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {member.role}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Mission Statement */}
        <motion.div 
          variants={itemVariants}
          className="bg-gradient-to-r from-olive/10 to-sand/10 rounded-2xl p-8 md:p-12 text-center"
        >
          <Sparkles className="h-12 w-12 text-olive mx-auto mb-6" />
          <h2 className={`text-3xl font-semibold mb-6 ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
            {t('about.mission.title')}
          </h2>
          <p className={`text-lg text-gray-700 max-w-4xl mx-auto leading-relaxed ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {t('about.mission.description')}
          </p>
        </motion.div>
      </motion.div>
    </Layout>
  );
};

export default About;
