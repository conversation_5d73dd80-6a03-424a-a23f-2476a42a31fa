
import Layout from '@/components/Layout';
import ProductCategoryLayout from '@/components/ProductCategoryLayout';
import { getProductsByCategory } from '@/data/products';
import { useLanguage } from '@/contexts/LanguageContext';
import { useReducedMotion, getAnimationVariants } from '@/hooks/useReducedMotion';
import { motion } from 'framer-motion';
import { ArrowRight, Leaf } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';

const Argan = () => {
  const { language, t, isRTL } = useLanguage();
  const prefersReducedMotion = useReducedMotion();

  const products = getProductsByCategory('argan').map(product => ({
    ...product,
    title: product.title[language as keyof typeof product.title],
    originalPrice: product.discountPercentage ? product.originalPrice : undefined,
  }));

  // Animation variants for container
  const containerVariants = prefersReducedMotion ? {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  } : {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  // Animation variants for elements
  const itemVariants = getAnimationVariants(prefersReducedMotion);

  return (
    <Layout>
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full px-4 sm:px-6 lg:px-8 py-8"
      >
        {/* Category Header */}
        <motion.div 
          variants={itemVariants} 
          className={`mb-8 border-b pb-4 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <div className="flex items-center gap-3 mb-2">
            <Leaf className="text-olive h-6 w-6" />
            <h1 className={`text-3xl font-semibold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
              {t('categories.argan')}
            </h1>
          </div>
          
          <motion.p 
            variants={itemVariants}
            className={`text-gray-600 mt-2 max-w-3xl ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
          >
            {language === 'ar' 
              ? 'زيت الأرغان المغربي الأصلي، مستخلص من نواة شجر الأرغان النادر. غني بالمغذيات والفيتامينات.'
              : language === 'fr'
                ? 'Huile d\'argan marocaine authentique, extraite des noix de l\'arbre d\'argan rare. Riche en nutriments et en vitamines.'
                : 'Authentic Moroccan argan oil, extracted from the rare argan tree nuts. Rich in nutrients and vitamins.'}
          </motion.p>
          
          <motion.div 
            variants={itemVariants}
            className="flex flex-wrap gap-2 mt-4"
          >
            <Badge className="bg-green-100 text-green-800 border-green-200">
              {language === 'ar' ? '100٪ طبيعي' : language === 'fr' ? '100% Naturel' : '100% Natural'}
            </Badge>
            <Badge className="bg-amber-100 text-amber-800 border-amber-200">
              {language === 'ar' ? 'تعاونيات نسائية' : language === 'fr' ? 'Coopératives Féminines' : 'Women\'s Cooperatives'}
            </Badge>
            <Badge className="bg-blue-100 text-blue-800 border-blue-200">
              {language === 'ar' ? 'منتج محلي' : language === 'fr' ? 'Produit Local' : 'Local Product'}
            </Badge>
          </motion.div>
        </motion.div>

        {/* Main Product Grid */}
        <motion.div variants={itemVariants}>
          <ProductCategoryLayout
            categoryId="argan"
            titleKey="argan"
            filterOptions={['pure', 'cosmetic']}
            products={products}
          />
        </motion.div>

        {/* Learn More Section */}
        <motion.div 
          variants={itemVariants}
          className={`mt-12 p-6 bg-olive/5 rounded-lg border border-olive/20 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <h3 className={`text-xl font-semibold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive mb-2`}>
            {language === 'ar' ? 'أصل زيت الأرغان' : language === 'fr' ? 'L\'origine de l\'huile d\'argan' : 'The Origin of Argan Oil'}
          </h3>
          <p className={`text-gray-700 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {language === 'ar' 
              ? 'يستخرج زيت الأرغان من بذور شجرة الأرغان التي تنمو حصريًا في جنوب غرب المغرب. تقوم النساء في التعاونيات المحلية بمعالجة البذور يدويًا باستخدام تقنيات تقليدية.'
              : language === 'fr'
                ? 'L\'huile d\'argan est extraite des graines de l\'arganier qui pousse exclusivement dans le sud-ouest du Maroc. Les femmes des coopératives locales traitent les graines à la main en utilisant des techniques traditionnelles.'
                : 'Argan oil is extracted from the kernels of the argan tree which grows exclusively in southwest Morocco. Women in local cooperatives process the kernels by hand using traditional techniques.'}
          </p>
          
          <Link to="#" className={`inline-flex items-center mt-4 text-olive hover:text-olive/80 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <span className={`font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
              {language === 'ar' ? 'اكتشف المزيد' : language === 'fr' ? 'En savoir plus' : 'Learn more'}
            </span>
            <ArrowRight className={`h-4 w-4 ${isRTL ? 'mr-2 rotate-180' : 'ml-2'}`} />
          </Link>
        </motion.div>
      </motion.div>
    </Layout>
  );
};

export default Argan;
