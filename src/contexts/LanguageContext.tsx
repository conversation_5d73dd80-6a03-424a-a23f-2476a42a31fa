
import React, { createContext, useContext, useState, useEffect } from 'react';
import { getTranslation } from '@/hooks/useI18n';

type Language = 'ar' | 'fr' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  isRTL: boolean;
  t: (key: string) => string;
}

// Create translations object
const translations = {
  ar: {
    // Header
    'home': 'الرئيسية',
    'morocco': 'المغرب',
    'argan': 'أركان',
    'amlou': 'أملو',
    'honey': 'عسل',
    'deals': 'العروض',
    'varieties': 'متنوعات',
    // Hero
    'hero.title': 'منتجات مغربية عضوية',
    'hero.subtitle': 'جودة ممتازة مباشرة من المغرب',
    'hero.cta': 'تسوق الآن',
    'hero.promo': 'زيت أركان عضوي - 20% خصم',
    // Categories
    'categories.title': 'تصفح فئاتنا',
    'categories.argan': 'زيت أركان',
    'categories.amlou': 'أملو',
    'categories.honey': 'عسل',
    'categories.deals': 'العروض',
    'categories.cosmetics': 'مستحضرات التجميل',
    // Filters
    'filters.honey.thyme': 'عسل الزعتر',
    'filters.honey.wildflower': 'عسل الزهور البرية',
    'filters.honey.orange': 'عسل زهور البرتقال',
    'filters.argan.pure': 'زيت أركان النقي',
    'filters.argan.cosmetic': 'زيت أركان للتجميل',
    // Products
    'product.details': 'تفاصيل المنتج',
    'product.certified': 'عضوي',
    'product.addToCart': 'أضف إلى السلة',
    // Footer
    'footer.contact': 'اتصل بنا',
    'footer.privacy': 'سياسة الخصوصية',
    'footer.terms': 'شروط الاستخدام',
    'footer.rights': 'جميع الحقوق محفوظة',
    'footer.cod': 'الدفع عند الاستلام',
  },
  fr: {
    // Header
    'home': 'Accueil',
    'morocco': 'Maroc',
    'argan': 'Argan',
    'amlou': 'Amlou',
    'honey': 'Miel',
    'deals': 'Offres',
    'varieties': 'Divers',
    // Hero
    'hero.title': 'Produits Bio Marocains',
    'hero.subtitle': 'Qualité premium directement du Maroc',
    'hero.cta': 'Acheter',
    'hero.promo': 'Huile d\'Argan Bio - 20% de réduction',
    // Categories
    'categories.title': 'Parcourir nos catégories',
    'categories.argan': 'Huile d\'Argan',
    'categories.amlou': 'Amlou',
    'categories.honey': 'Miel',
    'categories.deals': 'Promotions',
    'categories.cosmetics': 'Cosmétiques',
    // Filters
    'filters.honey.thyme': 'Miel de Thym',
    'filters.honey.wildflower': 'Miel de Fleurs Sauvages',
    'filters.honey.orange': 'Miel de Fleur d\'Oranger',
    'filters.argan.pure': 'Huile d\'Argan Pure',
    'filters.argan.cosmetic': 'Huile d\'Argan Cosmétique',
    // Products
    'product.details': 'Détails du produit',
    'product.certified': 'Certifié Bio',
    'product.addToCart': 'Ajouter au panier',
    // Footer
    'footer.contact': 'Contactez-nous',
    'footer.privacy': 'Politique de confidentialité',
    'footer.terms': 'Conditions d\'utilisation',
    'footer.rights': 'Tous droits réservés',
    'footer.cod': 'Paiement à la livraison',
  },
  en: {
    // Header
    'home': 'Home',
    'morocco': 'Morocco',
    'argan': 'Argan',
    'amlou': 'Amlou',
    'honey': 'Honey',
    'deals': 'Deals',
    'varieties': 'Varieties',
    // Hero
    'hero.title': 'Moroccan Organic Products',
    'hero.subtitle': 'Premium quality directly from Morocco',
    'hero.cta': 'Shop Now',
    'hero.promo': 'Organic Argan Oil - 20% Off',
    // Categories
    'categories.title': 'Browse Our Categories',
    'categories.argan': 'Argan Oil',
    'categories.amlou': 'Amlou',
    'categories.honey': 'Honey',
    'categories.deals': 'Deals',
    'categories.cosmetics': 'Cosmetics',
    // Filters
    'filters.honey.thyme': 'Thyme Honey',
    'filters.honey.wildflower': 'Wildflower Honey',
    'filters.honey.orange': 'Orange Blossom Honey',
    'filters.argan.pure': 'Pure Argan Oil',
    'filters.argan.cosmetic': 'Cosmetic Argan Oil',
    // Products
    'product.details': 'Product Details',
    'product.certified': 'Organic Certified',
    'product.addToCart': 'Add to Cart',
    // Footer
    'footer.contact': 'Contact Us',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
    'footer.rights': 'All Rights Reserved',
    'footer.cod': 'Cash on Delivery',
  }
};

const LanguageContext = createContext<LanguageContextType>({
  language: 'ar',
  setLanguage: () => {},
  isRTL: true,
  t: () => '',
});

export const useLanguage = () => useContext(LanguageContext);

export const LanguageProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  // Initialize language from localStorage or default to 'ar'
  const [language, setLanguageState] = useState<Language>(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('preferred-language') as Language;
      return savedLanguage && ['ar', 'fr', 'en'].includes(savedLanguage) ? savedLanguage : 'ar';
    }
    return 'ar';
  });

  const isRTL = language === 'ar';

  // Enhanced setLanguage function that persists to localStorage
  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('preferred-language', lang);
    }
  };

  // Enhanced translation function that supports both old and new key formats
  const t = (key: string): string => {
    // First try the new i18n system
    const newTranslation = getTranslation(key, language);
    if (newTranslation !== key) {
      return newTranslation;
    }

    // Fallback to old translation system for backward compatibility
    const oldTranslation = translations[language][key as keyof typeof translations[typeof language]];
    return oldTranslation || key;
  };

  // Set document direction when language changes
  useEffect(() => {
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    // Apply appropriate font family based on language
    if (language === 'ar') {
      document.body.classList.add('font-arabic');
      document.body.classList.remove('font-body');
    } else {
      document.body.classList.add('font-body');
      document.body.classList.remove('font-arabic');
    }
  }, [language, isRTL]);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, isRTL, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
