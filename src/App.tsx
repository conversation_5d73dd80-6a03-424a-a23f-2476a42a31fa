
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { CartProvider } from '@/contexts/CartContext';
import { FavoritesProvider } from '@/contexts/FavoritesContext';
import { CartToastProvider } from '@/components/CartToast';
import { Toaster } from '@/components/ui/sonner';
import Index from '@/pages/Index';
import ProductPage from '@/pages/ProductPage';
import ArganOil from '@/pages/ArganOil';
import Amlou from '@/pages/Amlou';
import Honey from '@/pages/Honey';
import Deals from '@/pages/Deals';
import About from '@/pages/About';
import Contact from '@/pages/Contact';
import Privacy from '@/pages/Privacy';
import Terms from '@/pages/Terms';
import NotFound from '@/pages/NotFound';
import './App.css';

function App() {
  return (
    <LanguageProvider>
      <FavoritesProvider>
        <CartProvider>
          <CartToastProvider>
            <Router>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route path="/product/:id" element={<ProductPage />} />
                <Route path="/argan" element={<ArganOil />} />
                <Route path="/amlou" element={<Amlou />} />
                <Route path="/honey" element={<Honey />} />
                <Route path="/deals" element={<Deals />} />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/privacy" element={<Privacy />} />
                <Route path="/terms" element={<Terms />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
              <Toaster />
            </Router>
          </CartToastProvider>
        </CartProvider>
      </FavoritesProvider>
    </LanguageProvider>
  );
}

export default App;
