import { orderApi, customerApi, Order as ApiOrder, Customer as ApiCustomer } from './api';
import { CartItem } from '@/contexts/CartContext';

export interface OrderData {
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    dateOfBirth?: string;
  };
  deliveryAddress: {
    streetAddress: string;
    city: string;
    postalCode: string;
    country: string;
    region: string;
  };
  paymentMethod: string;
  items: CartItem[];
  subtotal: number;
  shippingFee: number;
  total: number;
}

export interface OrderResult {
  success: boolean;
  orderId?: string;
  error?: string;
}

class OrderService {
  async createOrder(orderData: OrderData): Promise<OrderResult> {
    try {
      // Prepare order data for API
      const apiOrderData = {
        customerInfo: {
          firstName: orderData.customerInfo.firstName,
          lastName: orderData.customerInfo.lastName,
          email: orderData.customerInfo.email,
          phone: orderData.customerInfo.phone,
          dateOfBirth: orderData.customerInfo.dateOfBirth
        },
        shippingAddress: {
          type: 'shipping' as const,
          streetAddress: orderData.deliveryAddress.streetAddress,
          city: orderData.deliveryAddress.city,
          postalCode: orderData.deliveryAddress.postalCode,
          country: orderData.deliveryAddress.country,
          region: orderData.deliveryAddress.region,
          isDefault: false
        },
        paymentMethod: orderData.paymentMethod,
        items: orderData.items.map(item => ({
          productId: item.id,
          productTitle: item.title,
          productImage: item.image,
          productCategory: item.category,
          price: item.price,
          originalPrice: item.originalPrice,
          quantity: item.quantity,
          attributes: item.attributes,
          discountPercentage: item.discountPercentage
        })),
        subtotal: orderData.subtotal,
        shippingFee: orderData.shippingFee,
        total: orderData.total
      };

      // Create order via API
      const response = await orderApi.createOrder(apiOrderData);

      if (response.success && response.data) {
        const { orderId } = response.data;

        console.log(`Order ${orderId} created successfully`);

        // Email notifications will be handled by the backend API
        console.log(`Order ${orderId} created successfully - email notifications handled by backend`);

        return {
          success: true,
          orderId
        };
      } else {
        throw new Error(response.message || 'Failed to create order');
      }

    } catch (error) {
      console.error('Order creation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async getOrderDetails(orderId: string): Promise<{
    success: boolean;
    order?: any;
    error?: string;
  }> {
    try {
      const response = await orderApi.getOrderByOrderId(orderId);
      if (response.success && response.data) {
        return {
          success: true,
          order: response.data
        };
      } else {
        return {
          success: false,
          error: 'Order not found'
        };
      }
    } catch (error) {
      console.error('Failed to get order details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async updateOrderStatus(orderId: string, status: string): Promise<boolean> {
    try {
      // Note: This would require an admin API endpoint to update order status
      // For now, we'll just log the attempt
      console.log(`Order status update requested for ${orderId}: ${status}`);
      return true;
    } catch (error) {
      console.error('Failed to update order status:', error);
      return false;
    }
  }

  async getCustomerOrders(email: string): Promise<any[]> {
    try {
      // Note: This would require a customer API endpoint to get orders by email
      // For now, we'll return empty array
      console.log(`Customer orders requested for email: ${email}`);
      return [];
    } catch (error) {
      console.error('Failed to get customer orders:', error);
      return [];
    }
  }


}

export const orderService = new OrderService();
