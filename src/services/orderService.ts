import { orderApi, customer<PERSON>pi, Order as Api<PERSON>rde<PERSON>, Customer as Api<PERSON>ustomer } from './api';
import { CartItem } from '@/contexts/CartContext';

export interface OrderData {
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    age?: string;
    dateOfBirth?: string;
  };
  deliveryAddress: {
    streetAddress: string;
    city: string;
    postalCode: string;
    country: string;
    region: string;
  };
  paymentMethod: string;
  items: CartItem[];
  subtotal: number;
  shippingFee: number;
  total: number;
}

export interface OrderResult {
  success: boolean;
  orderId?: string;
  error?: string;
}

class OrderService {
  async createOrder(orderData: OrderData): Promise<OrderResult> {
    try {
      // First, create customer
      let customerId: string;

      // Normalize phone number to Moroccan format
      const normalizePhone = (phone: string): string => {
        // Remove all spaces and special characters except +
        let normalized = phone.replace(/[\s\-\(\)]/g, '');

        // If it starts with +212, keep as is
        if (normalized.startsWith('+212')) {
          return normalized;
        }

        // If it starts with 212, add +
        if (normalized.startsWith('212')) {
          return '+' + normalized;
        }

        // If it starts with 0, replace with +212
        if (normalized.startsWith('0')) {
          return '+212' + normalized.substring(1);
        }

        // If it's just 9 digits starting with 5, 6, or 7, add +212
        if (/^[5-7][0-9]{8}$/.test(normalized)) {
          return '+212' + normalized;
        }

        // Default: assume it needs +212 prefix
        return '+212' + normalized;
      };

      try {
        // Create unique email for this order to avoid conflicts
        const timestamp = Date.now();
        const uniqueEmail = `${orderData.customerInfo.email.split('@')[0]}_${timestamp}@${orderData.customerInfo.email.split('@')[1]}`;

        // Try to create customer for this order
        const customerResponse = await customerApi.createCustomer({
          firstName: orderData.customerInfo.firstName,
          lastName: orderData.customerInfo.lastName,
          email: uniqueEmail, // Use unique email to avoid conflicts
          phone: normalizePhone(orderData.customerInfo.phone),
          age: orderData.customerInfo.age || '26-35',
          preferredLanguage: 'fr'
        });

        if (customerResponse.success && customerResponse.data) {
          customerId = customerResponse.data._id;
          console.log('Customer created successfully:', customerId);
        } else {
          throw new Error('Failed to create customer: ' + (customerResponse.message || 'Unknown error'));
        }
      } catch (error) {
        console.error('Customer creation failed:', error);
        throw new Error('Failed to create customer: ' + (error instanceof Error ? error.message : 'Unknown error'));
      }

      // Convert payment method to backend format
      const paymentMethodMap: { [key: string]: string } = {
        'cashOnDelivery': 'cash_on_delivery',
        'creditCard': 'credit_card',
        'bankTransfer': 'bank_transfer',
        'paypal': 'paypal'
      };

      // Prepare order data for API
      const apiOrderData = {
        customerId,
        customerInfo: {
          firstName: orderData.customerInfo.firstName,
          lastName: orderData.customerInfo.lastName,
          email: orderData.customerInfo.email,
          phone: orderData.customerInfo.phone
        },
        shippingAddress: {
          streetAddress: orderData.deliveryAddress.streetAddress,
          city: orderData.deliveryAddress.city,
          postalCode: orderData.deliveryAddress.postalCode,
          country: orderData.deliveryAddress.country,
          region: orderData.deliveryAddress.region
        },
        paymentMethod: paymentMethodMap[orderData.paymentMethod] || 'cash_on_delivery',
        items: orderData.items.map(item => ({
          productId: item.id,
          productTitle: typeof item.title === 'string' ? item.title : item.title.fr || item.title.en || 'Product',
          productImage: item.image,
          productCategory: item.category,
          price: item.price,
          originalPrice: item.originalPrice,
          quantity: item.quantity,
          total: item.price * item.quantity // Calculate total for each item
        })),
        subtotal: orderData.subtotal,
        shippingFee: orderData.shippingFee,
        total: orderData.total,
        notes: '' // Add empty notes field
      };

      // Create order via API
      const response = await orderApi.createOrder(apiOrderData);

      if (response.success && response.data) {
        const { orderId } = response.data;

        console.log(`Order ${orderId} created successfully`);

        // Email notifications will be handled by the backend API
        console.log(`Order ${orderId} created successfully - email notifications handled by backend`);

        return {
          success: true,
          orderId
        };
      } else {
        throw new Error(response.message || 'Failed to create order');
      }

    } catch (error) {
      console.error('Order creation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async getOrderDetails(orderId: string): Promise<{
    success: boolean;
    order?: any;
    error?: string;
  }> {
    try {
      const response = await orderApi.getOrderByOrderId(orderId);
      if (response.success && response.data) {
        return {
          success: true,
          order: response.data
        };
      } else {
        return {
          success: false,
          error: 'Order not found'
        };
      }
    } catch (error) {
      console.error('Failed to get order details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async updateOrderStatus(orderId: string, status: string): Promise<boolean> {
    try {
      // Note: This would require an admin API endpoint to update order status
      // For now, we'll just log the attempt
      console.log(`Order status update requested for ${orderId}: ${status}`);
      return true;
    } catch (error) {
      console.error('Failed to update order status:', error);
      return false;
    }
  }

  async getCustomerOrders(email: string): Promise<any[]> {
    try {
      // Note: This would require a customer API endpoint to get orders by email
      // For now, we'll return empty array
      console.log(`Customer orders requested for email: ${email}`);
      return [];
    } catch (error) {
      console.error('Failed to get customer orders:', error);
      return [];
    }
  }


}

export const orderService = new OrderService();
