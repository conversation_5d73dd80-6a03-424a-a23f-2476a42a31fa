import { databaseService, Customer, DeliveryAddress, Order, OrderItem } from './database';
import { CartItem } from '@/contexts/CartContext';
import { emailService, OrderConfirmationData } from './emailService';

export interface OrderData {
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    age: string;
    dateOfBirth?: string;
  };
  deliveryAddress: {
    streetAddress: string;
    city: string;
    postalCode: string;
    country: string;
    region: string;
  };
  paymentMethod: string;
  items: CartItem[];
  subtotal: number;
  shippingFee: number;
  total: number;
}

export interface OrderResult {
  success: boolean;
  orderId?: string;
  error?: string;
}

class OrderService {
  async createOrder(orderData: OrderData): Promise<OrderResult> {
    try {
      // Generate unique order ID
      const orderId = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // Check if customer already exists
      let customerId: number;
      const existingCustomer = await databaseService.getCustomerByEmail(orderData.customerInfo.email);
      
      if (existingCustomer) {
        customerId = existingCustomer.id!;
      } else {
        // Create new customer
        const customer: Customer = {
          firstName: orderData.customerInfo.firstName,
          lastName: orderData.customerInfo.lastName,
          email: orderData.customerInfo.email,
          phone: orderData.customerInfo.phone,
          age: orderData.customerInfo.age,
          dateOfBirth: orderData.customerInfo.dateOfBirth
        };
        
        customerId = await databaseService.createCustomer(customer);
      }

      // Create delivery address
      const address: DeliveryAddress = {
        customerId,
        streetAddress: orderData.deliveryAddress.streetAddress,
        city: orderData.deliveryAddress.city,
        postalCode: orderData.deliveryAddress.postalCode,
        country: orderData.deliveryAddress.country,
        region: orderData.deliveryAddress.region
      };
      
      const addressId = await databaseService.createDeliveryAddress(address);

      // Calculate estimated delivery (7 days from now)
      const estimatedDelivery = new Date();
      estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);

      // Create order
      const order: Order = {
        orderId,
        customerId,
        addressId,
        paymentMethod: orderData.paymentMethod,
        subtotal: orderData.subtotal,
        shippingFee: orderData.shippingFee,
        total: orderData.total,
        status: 'pending',
        orderDate: new Date().toISOString(),
        estimatedDelivery: estimatedDelivery.toISOString()
      };
      
      const orderDbId = await databaseService.createOrder(order);

      // Create order items
      for (const item of orderData.items) {
        const orderItem: OrderItem = {
          orderId: orderDbId,
          productId: item.id,
          productTitle: item.title,
          productImage: item.image,
          productCategory: item.category,
          price: item.price,
          originalPrice: item.originalPrice,
          quantity: item.quantity,
          attributes: item.attributes ? JSON.stringify(item.attributes) : undefined,
          discountPercentage: item.discountPercentage
        };
        
        await databaseService.createOrderItem(orderItem);
      }

      console.log(`Order ${orderId} created successfully`);

      // Send email notifications
      try {
        const confirmationData = this.generateOrderConfirmation(orderData, orderId);

        // Send confirmation email to customer
        const customerEmailResult = await emailService.sendOrderConfirmation(confirmationData);
        if (customerEmailResult.success) {
          console.log('Order confirmation email sent to customer');
          if (customerEmailResult.previewUrl) {
            console.log('Email preview:', customerEmailResult.previewUrl);
          }
        } else {
          console.error('Failed to send customer email:', customerEmailResult.error);
        }

        // Send invoice email to business owner
        const ownerEmailResult = await emailService.sendInvoiceToOwner(confirmationData);
        if (ownerEmailResult.success) {
          console.log('Invoice email sent to business owner');
        } else {
          console.error('Failed to send owner email:', ownerEmailResult.error);
        }
      } catch (emailError) {
        console.error('Email notification error:', emailError);
        // Don't fail the order creation if email fails
      }

      return {
        success: true,
        orderId
      };
      
    } catch (error) {
      console.error('Failed to create order:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async getOrderDetails(orderId: string): Promise<{
    order: Order | null;
    customer: Customer | null;
    address: DeliveryAddress | null;
    items: OrderItem[];
  }> {
    try {
      const order = await databaseService.getOrderById(orderId);
      if (!order) {
        return { order: null, customer: null, address: null, items: [] };
      }

      // Get customer details
      const customer = await databaseService.getCustomerByEmail(''); // We need to modify this
      
      // Get order items
      const items = await databaseService.getOrderItems(order.id!);

      return {
        order,
        customer,
        address: null, // We need to implement getAddressById
        items
      };
    } catch (error) {
      console.error('Failed to get order details:', error);
      return { order: null, customer: null, address: null, items: [] };
    }
  }

  async updateOrderStatus(orderId: string, status: Order['status']): Promise<boolean> {
    try {
      await databaseService.updateOrderStatus(orderId, status);
      console.log(`Order ${orderId} status updated to ${status}`);
      return true;
    } catch (error) {
      console.error('Failed to update order status:', error);
      return false;
    }
  }

  async getCustomerOrders(email: string): Promise<Order[]> {
    try {
      const customer = await databaseService.getCustomerByEmail(email);
      if (!customer) {
        return [];
      }

      return await databaseService.getOrderHistory(customer.id!);
    } catch (error) {
      console.error('Failed to get customer orders:', error);
      return [];
    }
  }

  // Generate order confirmation data for email
  generateOrderConfirmation(orderData: OrderData, orderId: string) {
    const orderDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
    const deliveryDate = estimatedDelivery.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    return {
      orderId,
      orderDate,
      estimatedDelivery: deliveryDate,
      customer: {
        name: `${orderData.customerInfo.firstName} ${orderData.customerInfo.lastName}`,
        email: orderData.customerInfo.email,
        phone: orderData.customerInfo.phone
      },
      deliveryAddress: {
        fullAddress: `${orderData.deliveryAddress.streetAddress}, ${orderData.deliveryAddress.city}, ${orderData.deliveryAddress.postalCode}, ${orderData.deliveryAddress.region}, ${orderData.deliveryAddress.country}`
      },
      paymentMethod: orderData.paymentMethod,
      items: orderData.items.map(item => ({
        title: item.title,
        quantity: item.quantity,
        price: item.price,
        total: item.price * item.quantity,
        image: item.image
      })),
      pricing: {
        subtotal: orderData.subtotal,
        shippingFee: orderData.shippingFee,
        total: orderData.total
      }
    };
  }
}

export const orderService = new OrderService();
