// API Configuration and Base Service
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiError {
  success: false;
  message: string;
  errors?: any[];
}

class ApiService {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // GET request
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const searchParams = params ? new URLSearchParams(params).toString() : '';
    const url = searchParams ? `${endpoint}?${searchParams}` : endpoint;
    
    return this.request<T>(url, {
      method: 'GET',
    });
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

export const apiService = new ApiService();

// Category interface (simplified for now)
export interface ICategory {
  _id: string;
  name: {
    ar: string;
    fr: string;
    en: string;
  };
  slug: {
    ar: string;
    fr: string;
    en: string;
  };
  image?: string;
}

// Product API Types
export interface Product {
  _id: string;
  title: {
    ar: string;
    fr: string;
    en: string;
  };
  description: {
    ar: string;
    fr: string;
    en: string;
  };
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  categoryId: string | ICategory;
  subcategoryId?: string | ICategory;
  images: string[];
  tags: string[];
  isOrganic: boolean;
  inStock: boolean;
  stockQuantity: number;
  weight?: string;
  volume?: string;
  ingredients?: {
    ar: string[];
    fr: string[];
    en: string[];
  };
  benefits?: {
    ar: string[];
    fr: string[];
    en: string[];
  };
  usage?: {
    ar: string;
    fr: string;
    en: string;
  };
  origin?: string;
  certifications?: string[];
  isActive: boolean;
  featured: boolean;
  seoTitle?: {
    ar: string;
    fr: string;
    en: string;
  };
  seoDescription?: {
    ar: string;
    fr: string;
    en: string;
  };
  slug?: {
    ar: string;
    fr: string;
    en: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Customer API Types
export interface Customer {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  totalSpent: number;
  orderCount: number;
  lastOrderDate?: string;
  isActive: boolean;
  addresses: Address[];
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  _id: string;
  type: 'shipping' | 'billing' | 'both';
  streetAddress: string;
  city: string;
  postalCode: string;
  country: string;
  region: string;
  isDefault: boolean;
}

// Order API Types
export interface Order {
  _id: string;
  orderId: string;
  customerId: string;
  items: OrderItem[];
  subtotal: number;
  tax?: number;
  discount?: number;
  shippingFee: number;
  total: number;
  orderStatus: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded';
  paymentMethod: string;
  shippingAddress: Address;
  billingAddress?: Address;
  trackingNumber?: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  notes?: string;
  orderDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  productTitle: string;
  productImage: string;
  productCategory: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  total: number;
  attributes?: Record<string, any>;
  discountPercentage?: number;
}

// Product API Service
export class ProductApiService {
  // Get all products with optional filtering
  async getProducts(params?: {
    page?: number;
    limit?: number;
    category?: string;
    featured?: boolean;
    inStock?: boolean;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    tags?: string[];
    isOrganic?: boolean;
  }): Promise<ApiResponse<Product[]>> {
    return apiService.get<Product[]>('/products', params);
  }

  // Get products by category
  async getProductsByCategory(
    category: string,
    params?: {
      page?: number;
      limit?: number;
      subcategory?: string;
      featured?: boolean;
      inStock?: boolean;
    }
  ): Promise<ApiResponse<Product[]>> {
    return apiService.get<Product[]>(`/products`, { ...params, categoryId: category });
  }

  // Get products by category ID
  async getProductsByCategoryId(
    categoryId: string,
    params?: {
      page?: number;
      limit?: number;
      subcategoryId?: string;
      featured?: boolean;
      inStock?: boolean;
    }
  ): Promise<ApiResponse<Product[]>> {
    return apiService.get<Product[]>(`/products`, { ...params, categoryId });
  }

  // Get featured products
  async getFeaturedProducts(): Promise<ApiResponse<Product[]>> {
    return apiService.get<Product[]>('/products/featured');
  }

  // Search products
  async searchProducts(query: string, params?: {
    page?: number;
    limit?: number;
    category?: string;
  }): Promise<ApiResponse<Product[]>> {
    return apiService.get<Product[]>('/products/search', { q: query, ...params });
  }

  // Get product by ID
  async getProductById(id: string): Promise<ApiResponse<Product>> {
    return apiService.get<Product>(`/products/${id}`);
  }

  // Get product by slug
  async getProductBySlug(slug: string, lang?: string): Promise<ApiResponse<Product>> {
    return apiService.get<Product>(`/products/slug/${slug}${lang ? `/${lang}` : ''}`);
  }
}

// Customer API Service
export class CustomerApiService {
  // Create customer
  async createCustomer(customerData: Omit<Customer, '_id' | 'tier' | 'totalSpent' | 'orderCount' | 'isActive' | 'addresses' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Customer>> {
    return apiService.post<Customer>('/customers', customerData);
  }

  // Get all customers
  async getCustomers(): Promise<ApiResponse<Customer[]>> {
    return apiService.get<Customer[]>('/customers');
  }
}

// Order API Service
export class OrderApiService {
  // Create order
  async createOrder(orderData: {
    customerInfo: {
      firstName: string;
      lastName: string;
      email: string;
      phone: string;
      dateOfBirth?: string;
    };
    shippingAddress: Omit<Address, '_id'>;
    billingAddress?: Omit<Address, '_id'>;
    paymentMethod: string;
    items: {
      productId: string;
      productTitle: string;
      productImage: string;
      productCategory: string;
      price: number;
      originalPrice?: number;
      quantity: number;
      attributes?: Record<string, any>;
      discountPercentage?: number;
    }[];
    subtotal: number;
    shippingFee: number;
    total: number;
    notes?: string;
  }): Promise<ApiResponse<{ orderId: string; order: Order }>> {
    return apiService.post<{ orderId: string; order: Order }>('/orders', orderData);
  }

  // Get order by order ID
  async getOrderByOrderId(orderId: string): Promise<ApiResponse<Order>> {
    return apiService.get<Order>(`/orders/order/${orderId}`);
  }
}

// Export service instances
export const productApi = new ProductApiService();
export const customerApi = new CustomerApiService();
export const orderApi = new OrderApiService();
