import nodemailer from 'nodemailer';

export interface OrderConfirmationData {
  orderId: string;
  orderDate: string;
  estimatedDelivery: string;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  deliveryAddress: {
    fullAddress: string;
  };
  paymentMethod: string;
  items: Array<{
    title: string;
    quantity: number;
    price: number;
    total: number;
    image: string;
  }>;
  pricing: {
    subtotal: number;
    shippingFee: number;
    total: number;
  };
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private isConfigured = false;

  constructor() {
    this.initializeTransporter();
  }

  private async initializeTransporter() {
    try {
      // For development, we'll use a test account
      // In production, you would use your actual email service credentials
      const testAccount = await nodemailer.createTestAccount();

      this.transporter = nodemailer.createTransporter({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
      });

      this.isConfigured = true;
      console.log('Email service initialized with test account');
      console.log('Test account credentials:', {
        user: testAccount.user,
        pass: testAccount.pass
      });
    } catch (error) {
      console.error('Failed to initialize email service:', error);
      this.isConfigured = false;
    }
  }

  private generateOrderConfirmationHTML(data: OrderConfirmationData): string {
    const itemsHTML = data.items.map(item => `
      <tr style="border-bottom: 1px solid #eee;">
        <td style="padding: 15px; text-align: left;">
          <div style="display: flex; align-items: center; gap: 15px;">
            <img src="${item.image}" alt="${item.title}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
            <div>
              <h4 style="margin: 0; color: #333; font-size: 16px;">${item.title}</h4>
              <p style="margin: 5px 0 0 0; color: #666; font-size: 14px;">Quantity: ${item.quantity}</p>
            </div>
          </div>
        </td>
        <td style="padding: 15px; text-align: right; color: #333; font-weight: 600;">
          ${item.price.toFixed(2)} MAD
        </td>
        <td style="padding: 15px; text-align: right; color: #333; font-weight: 600;">
          ${item.total.toFixed(2)} MAD
        </td>
      </tr>
    `).join('');

    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Order Confirmation - Imlil Bio Boutique</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f8f9fa;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #8B7355 0%, #A0956B 100%); color: white; padding: 30px; text-align: center;">
            <h1 style="margin: 0; font-size: 28px; font-weight: 700;">Imlil Bio Boutique</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Natural Products from Morocco</p>
          </div>

          <!-- Order Confirmation -->
          <div style="padding: 30px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h2 style="color: #333; margin: 0 0 10px 0; font-size: 24px;">Order Confirmation</h2>
              <p style="color: #666; margin: 0; font-size: 16px;">Thank you for your order, ${data.customer.name}!</p>
            </div>

            <!-- Order Details -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Order Details</h3>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                  <p style="margin: 0; color: #666; font-size: 14px;">Order ID</p>
                  <p style="margin: 5px 0 0 0; color: #333; font-weight: 600; font-size: 16px;">${data.orderId}</p>
                </div>
                <div>
                  <p style="margin: 0; color: #666; font-size: 14px;">Order Date</p>
                  <p style="margin: 5px 0 0 0; color: #333; font-weight: 600; font-size: 16px;">${data.orderDate}</p>
                </div>
                <div>
                  <p style="margin: 0; color: #666; font-size: 14px;">Payment Method</p>
                  <p style="margin: 5px 0 0 0; color: #333; font-weight: 600; font-size: 16px;">${data.paymentMethod}</p>
                </div>
                <div>
                  <p style="margin: 0; color: #666; font-size: 14px;">Estimated Delivery</p>
                  <p style="margin: 5px 0 0 0; color: #333; font-weight: 600; font-size: 16px;">${data.estimatedDelivery}</p>
                </div>
              </div>
            </div>

            <!-- Delivery Address -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Delivery Address</h3>
              <p style="margin: 0; color: #333; font-size: 16px; line-height: 1.5;">${data.deliveryAddress.fullAddress}</p>
            </div>

            <!-- Order Items -->
            <div style="margin-bottom: 30px;">
              <h3 style="color: #333; margin: 0 0 20px 0; font-size: 18px;">Order Items</h3>
              <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                <thead>
                  <tr style="background-color: #f8f9fa;">
                    <th style="padding: 15px; text-align: left; color: #333; font-weight: 600;">Product</th>
                    <th style="padding: 15px; text-align: right; color: #333; font-weight: 600;">Price</th>
                    <th style="padding: 15px; text-align: right; color: #333; font-weight: 600;">Total</th>
                  </tr>
                </thead>
                <tbody>
                  ${itemsHTML}
                </tbody>
              </table>
            </div>

            <!-- Order Summary -->
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Order Summary</h3>
              <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <span style="color: #666;">Subtotal:</span>
                <span style="color: #333; font-weight: 600;">${data.pricing.subtotal.toFixed(2)} MAD</span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                <span style="color: #666;">Shipping:</span>
                <span style="color: #333; font-weight: 600;">
                  ${data.pricing.shippingFee === 0 ? 'Free' : `${data.pricing.shippingFee.toFixed(2)} MAD`}
                </span>
              </div>
              <hr style="border: none; border-top: 1px solid #ddd; margin: 15px 0;">
              <div style="display: flex; justify-content: space-between; font-size: 18px;">
                <span style="color: #333; font-weight: 700;">Total:</span>
                <span style="color: #8B7355; font-weight: 700;">${data.pricing.total.toFixed(2)} MAD</span>
              </div>
            </div>

            <!-- Contact Information -->
            <div style="margin-top: 30px; padding-top: 30px; border-top: 1px solid #eee; text-align: center;">
              <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Need Help?</h3>
              <p style="color: #666; margin: 0 0 10px 0;">Contact us if you have any questions about your order.</p>
              <p style="color: #8B7355; margin: 0; font-weight: 600;"><EMAIL></p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #333; color: white; padding: 20px; text-align: center;">
            <p style="margin: 0; font-size: 14px; opacity: 0.8;">
              © 2024 Imlil Bio Boutique. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  async sendOrderConfirmation(data: OrderConfirmationData): Promise<{
    success: boolean;
    messageId?: string;
    previewUrl?: string;
    error?: string;
  }> {
    if (!this.isConfigured || !this.transporter) {
      return {
        success: false,
        error: 'Email service not configured'
      };
    }

    try {
      const htmlContent = this.generateOrderConfirmationHTML(data);

      const mailOptions = {
        from: '"Imlil Bio Boutique" <<EMAIL>>',
        to: data.customer.email,
        bcc: '<EMAIL>', // Send copy to business owner
        subject: `Order Confirmation - ${data.orderId}`,
        html: htmlContent,
        text: `
Order Confirmation - ${data.orderId}

Dear ${data.customer.name},

Thank you for your order! Here are the details:

Order ID: ${data.orderId}
Order Date: ${data.orderDate}
Estimated Delivery: ${data.estimatedDelivery}

Delivery Address:
${data.deliveryAddress.fullAddress}

Order Items:
${data.items.map(item => `- ${item.title} (Qty: ${item.quantity}) - ${item.total.toFixed(2)} MAD`).join('\n')}

Order Summary:
Subtotal: ${data.pricing.subtotal.toFixed(2)} MAD
Shipping: ${data.pricing.shippingFee === 0 ? 'Free' : `${data.pricing.shippingFee.toFixed(2)} MAD`}
Total: ${data.pricing.total.toFixed(2)} MAD

If you have any questions, please contact <NAME_EMAIL>

Best regards,
Imlil Bio Boutique Team
        `
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      // Get preview URL for test emails
      const previewUrl = nodemailer.getTestMessageUrl(info);

      console.log('Order confirmation email sent successfully');
      console.log('Message ID:', info.messageId);
      if (previewUrl) {
        console.log('Preview URL:', previewUrl);
      }

      return {
        success: true,
        messageId: info.messageId,
        previewUrl: previewUrl || undefined
      };

    } catch (error) {
      console.error('Failed to send order confirmation email:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async sendInvoiceToOwner(data: OrderConfirmationData): Promise<{
    success: boolean;
    messageId?: string;
    error?: string;
  }> {
    if (!this.isConfigured || !this.transporter) {
      return {
        success: false,
        error: 'Email service not configured'
      };
    }

    try {
      const htmlContent = this.generateOrderConfirmationHTML(data);

      const mailOptions = {
        from: '"Imlil Bio Boutique System" <<EMAIL>>',
        to: '<EMAIL>',
        subject: `New Order Received - ${data.orderId}`,
        html: htmlContent.replace('Order Confirmation', 'New Order Received'),
        text: `
New Order Received - ${data.orderId}

Customer: ${data.customer.name}
Email: ${data.customer.email}
Phone: ${data.customer.phone}

Order Details:
Order ID: ${data.orderId}
Order Date: ${data.orderDate}
Payment Method: ${data.paymentMethod}

Delivery Address:
${data.deliveryAddress.fullAddress}

Order Items:
${data.items.map(item => `- ${item.title} (Qty: ${item.quantity}) - ${item.total.toFixed(2)} MAD`).join('\n')}

Order Total: ${data.pricing.total.toFixed(2)} MAD

Please process this order as soon as possible.
        `
      };

      const info = await this.transporter.sendMail(mailOptions);

      console.log('Invoice email sent to owner successfully');
      console.log('Message ID:', info.messageId);

      return {
        success: true,
        messageId: info.messageId
      };

    } catch (error) {
      console.error('Failed to send invoice email to owner:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }
}

export const emailService = new EmailService();
