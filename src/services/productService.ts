import { productApi, Product as ApiProduct } from './api';

// Transform API product to frontend product format
export interface Product {
  id: string;
  title: {
    ar: string;
    fr: string;
    en: string;
  };
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  category: string;
  image: string;
  isOrganic?: boolean;
  tags?: string[];
  description?: {
    ar: string;
    fr: string;
    en: string;
  };
  benefits?: string[];
  extractionMethod?: string;
  viscosity?: string;
  producer?: {
    name: string;
    location: string;
    womenMembers: number;
  };
  inStock?: boolean;
  stockQuantity?: number;
  weight?: string;
  volume?: string;
  ingredients?: {
    ar: string[];
    fr: string[];
    en: string[];
  };
  usage?: {
    ar: string;
    fr: string;
    en: string;
  };
  origin?: string;
  certifications?: string[];
  featured?: boolean;
  slug?: {
    ar: string;
    fr: string;
    en: string;
  };
}

// Transform API product to frontend format
const transformApiProduct = (apiProduct: ApiProduct): Product => {
  return {
    id: apiProduct._id,
    title: apiProduct.title,
    price: apiProduct.price,
    originalPrice: apiProduct.originalPrice,
    discountPercentage: apiProduct.discountPercentage,
    category: typeof apiProduct.categoryId === 'string' ? apiProduct.categoryId : apiProduct.categoryId?._id || '',
    image: apiProduct.images?.[0] || '/placeholder.svg',
    isOrganic: apiProduct.isOrganic,
    tags: apiProduct.tags,
    description: apiProduct.description,
    benefits: apiProduct.benefits?.en || [], // Default to English for now
    inStock: apiProduct.inStock,
    stockQuantity: apiProduct.stockQuantity,
    weight: apiProduct.weight,
    volume: apiProduct.volume,
    ingredients: apiProduct.ingredients,
    usage: apiProduct.usage,
    origin: apiProduct.origin,
    certifications: apiProduct.certifications,
    featured: apiProduct.featured,
    slug: apiProduct.slug,
  };
};

class ProductService {
  private cache = new Map<string, { data: Product[]; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  // Check if cache is valid
  private isCacheValid(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < this.cacheTimeout;
  }

  // Get cached data
  private getCachedData(key: string): Product[] | null {
    if (this.isCacheValid(key)) {
      return this.cache.get(key)?.data || null;
    }
    return null;
  }

  // Set cache data
  private setCacheData(key: string, data: Product[]): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    const cacheKey = 'all_products';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await productApi.getProducts({ limit: 100 });
      if (response.success && response.data) {
        const products = response.data.map(transformApiProduct);
        this.setCacheData(cacheKey, products);
        return products;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch products:', error);
      // Return empty array if API fails - no fallback needed with MongoDB Atlas
      return [];
    }
  }

  // Get products by category
  async getProductsByCategory(category: string): Promise<Product[]> {
    const cacheKey = `category_${category}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      if (category === 'deals') {
        // For deals, get all products and filter by discount
        const allProducts = await this.getAllProducts();
        const dealsProducts = allProducts.filter(p => p.discountPercentage && p.discountPercentage > 0);
        this.setCacheData(cacheKey, dealsProducts);
        return dealsProducts;
      }

      const response = await productApi.getProductsByCategory(category, { limit: 50 });
      if (response.success && response.data) {
        const products = response.data.map(transformApiProduct);
        this.setCacheData(cacheKey, products);
        return products;
      }
      return [];
    } catch (error) {
      console.error(`Failed to fetch products for category ${category}:`, error);
      // Return empty array if API fails - no fallback needed with MongoDB Atlas
      return [];
    }
  }

  // Get featured products
  async getFeaturedProducts(): Promise<Product[]> {
    const cacheKey = 'featured_products';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await productApi.getFeaturedProducts();
      if (response.success && response.data) {
        const products = response.data.map(transformApiProduct);
        this.setCacheData(cacheKey, products);
        return products;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch featured products:', error);
      // Return empty array if API fails - no fallback needed with MongoDB Atlas
      return [];
    }
  }

  // Get product by ID
  async getProductById(id: string): Promise<Product | null> {
    try {
      const response = await productApi.getProductById(id);
      if (response.success && response.data) {
        return transformApiProduct(response.data);
      }
      return null;
    } catch (error) {
      console.error(`Failed to fetch product ${id}:`, error);
      return null;
    }
  }

  // Search products
  async searchProducts(query: string, category?: string): Promise<Product[]> {
    try {
      const response = await productApi.searchProducts(query, { category, limit: 50 });
      if (response.success && response.data) {
        return response.data.map(transformApiProduct);
      }
      return [];
    } catch (error) {
      console.error(`Failed to search products for query "${query}":`, error);
      // Return empty array if API fails - no fallback needed with MongoDB Atlas
      return [];
    }
  }

  // Note: Fallback methods removed - using MongoDB Atlas exclusively

  // Clear cache (useful for refreshing data)
  clearCache(): void {
    this.cache.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(key: string): void {
    this.cache.delete(key);
  }
}

// Export singleton instance
export const productService = new ProductService();

// Export helper functions for backward compatibility
export const getProductsByCategory = async (category: string): Promise<Product[]> => {
  return productService.getProductsByCategory(category);
};

export const getProductById = async (id: string): Promise<Product | null> => {
  return productService.getProductById(id);
};

export const getAllProducts = async (): Promise<Product[]> => {
  return productService.getAllProducts();
};

export const getFeaturedProducts = async (): Promise<Product[]> => {
  return productService.getFeaturedProducts();
};

export const searchProducts = async (query: string, category?: string): Promise<Product[]> => {
  return productService.searchProducts(query, category);
};
