import sqlite3 from 'sqlite3';
import { open, Database } from 'sqlite';
import path from 'path';

export interface Customer {
  id?: number;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  age: string;
  dateOfBirth?: string;
  createdAt?: string;
}

export interface DeliveryAddress {
  id?: number;
  customerId: number;
  streetAddress: string;
  city: string;
  postalCode: string;
  country: string;
  region: string;
}

export interface Order {
  id?: number;
  orderId: string;
  customerId: number;
  addressId: number;
  paymentMethod: string;
  subtotal: number;
  shippingFee: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  orderDate: string;
  estimatedDelivery?: string;
  createdAt?: string;
}

export interface OrderItem {
  id?: number;
  orderId: number;
  productId: string;
  productTitle: string;
  productImage: string;
  productCategory: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  attributes?: string; // JSON string for product attributes
  discountPercentage?: number;
}

class DatabaseService {
  private db: Database | null = null;
  private dbPath: string;

  constructor() {
    // Use a database file in the project root
    this.dbPath = path.join(process.cwd(), 'orders.db');
  }

  async initialize(): Promise<void> {
    try {
      this.db = await open({
        filename: this.dbPath,
        driver: sqlite3.Database
      });

      await this.createTables();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Create customers table
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        phone TEXT NOT NULL,
        age TEXT NOT NULL,
        dateOfBirth TEXT,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create delivery_addresses table
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS delivery_addresses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customerId INTEGER NOT NULL,
        streetAddress TEXT NOT NULL,
        city TEXT NOT NULL,
        postalCode TEXT NOT NULL,
        country TEXT NOT NULL,
        region TEXT NOT NULL,
        FOREIGN KEY (customerId) REFERENCES customers (id)
      )
    `);

    // Create orders table
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId TEXT NOT NULL UNIQUE,
        customerId INTEGER NOT NULL,
        addressId INTEGER NOT NULL,
        paymentMethod TEXT NOT NULL,
        subtotal REAL NOT NULL,
        shippingFee REAL NOT NULL,
        total REAL NOT NULL,
        status TEXT DEFAULT 'pending',
        orderDate DATETIME NOT NULL,
        estimatedDelivery DATETIME,
        createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customerId) REFERENCES customers (id),
        FOREIGN KEY (addressId) REFERENCES delivery_addresses (id)
      )
    `);

    // Create order_items table
    await this.db.exec(`
      CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId INTEGER NOT NULL,
        productId TEXT NOT NULL,
        productTitle TEXT NOT NULL,
        productImage TEXT NOT NULL,
        productCategory TEXT NOT NULL,
        price REAL NOT NULL,
        originalPrice REAL,
        quantity INTEGER NOT NULL,
        attributes TEXT,
        discountPercentage REAL,
        FOREIGN KEY (orderId) REFERENCES orders (id)
      )
    `);

    console.log('Database tables created successfully');
  }

  async createCustomer(customer: Customer): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.run(`
      INSERT INTO customers (firstName, lastName, email, phone, age, dateOfBirth)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      customer.firstName,
      customer.lastName,
      customer.email,
      customer.phone,
      customer.age,
      customer.dateOfBirth || null
    ]);

    return result.lastID!;
  }

  async createDeliveryAddress(address: DeliveryAddress): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.run(`
      INSERT INTO delivery_addresses (customerId, streetAddress, city, postalCode, country, region)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      address.customerId,
      address.streetAddress,
      address.city,
      address.postalCode,
      address.country,
      address.region
    ]);

    return result.lastID!;
  }

  async createOrder(order: Order): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.run(`
      INSERT INTO orders (orderId, customerId, addressId, paymentMethod, subtotal, shippingFee, total, status, orderDate, estimatedDelivery)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      order.orderId,
      order.customerId,
      order.addressId,
      order.paymentMethod,
      order.subtotal,
      order.shippingFee,
      order.total,
      order.status || 'pending',
      order.orderDate,
      order.estimatedDelivery || null
    ]);

    return result.lastID!;
  }

  async createOrderItem(item: OrderItem): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.run(`
      INSERT INTO order_items (orderId, productId, productTitle, productImage, productCategory, price, originalPrice, quantity, attributes, discountPercentage)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      item.orderId,
      item.productId,
      item.productTitle,
      item.productImage,
      item.productCategory,
      item.price,
      item.originalPrice || null,
      item.quantity,
      item.attributes || null,
      item.discountPercentage || null
    ]);

    return result.lastID!;
  }

  async getCustomerByEmail(email: string): Promise<Customer | null> {
    if (!this.db) throw new Error('Database not initialized');

    const customer = await this.db.get(`
      SELECT * FROM customers WHERE email = ?
    `, [email]);

    return customer || null;
  }

  async getOrderById(orderId: string): Promise<Order | null> {
    if (!this.db) throw new Error('Database not initialized');

    const order = await this.db.get(`
      SELECT * FROM orders WHERE orderId = ?
    `, [orderId]);

    return order || null;
  }

  async getOrderItems(orderDbId: number): Promise<OrderItem[]> {
    if (!this.db) throw new Error('Database not initialized');

    const items = await this.db.all(`
      SELECT * FROM order_items WHERE orderId = ?
    `, [orderDbId]);

    return items;
  }

  async getOrderHistory(customerId: number): Promise<Order[]> {
    if (!this.db) throw new Error('Database not initialized');

    const orders = await this.db.all(`
      SELECT * FROM orders WHERE customerId = ? ORDER BY createdAt DESC
    `, [customerId]);

    return orders;
  }

  async updateOrderStatus(orderId: string, status: Order['status']): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.run(`
      UPDATE orders SET status = ? WHERE orderId = ?
    `, [status, orderId]);
  }

  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      this.db = null;
    }
  }
}

// Create a singleton instance
export const databaseService = new DatabaseService();

// Initialize the database when the module is imported
databaseService.initialize().catch(console.error);
