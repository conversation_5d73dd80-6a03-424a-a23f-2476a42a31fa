import { ApiResponse, PaginationParams, PaginationResponse } from '@/types/api';

export interface IMultiLanguageString {
  ar: string;
  fr: string;
  en: string;
}

export interface ICategory {
  _id: string;
  name: IMultiLanguageString;
  slug: IMultiLanguageString;
  description: IMultiLanguageString;
  image?: string;
  parentCategory?: string | ICategory;
  isActive: boolean;
  sortOrder: number;
  seo?: {
    title: IMultiLanguageString;
    description: IMultiLanguageString;
    keywords: {
      ar: string[];
      fr: string[];
      en: string[];
    };
  };
  children?: ICategory[];
  productsCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CategoryQuery extends PaginationParams {
  isActive?: boolean;
  parentCategory?: string | null;
  language?: 'ar' | 'fr' | 'en';
}

export interface CategoryTreeResponse extends ApiResponse {
  data: ICategory[];
}

export interface CategoryResponse extends ApiResponse {
  data: ICategory;
}

export interface CategoriesResponse extends ApiResponse {
  data: ICategory[];
  pagination: PaginationResponse;
}

export interface CategoryProductsResponse extends ApiResponse {
  data: {
    category: ICategory;
    products: any[];
    pagination: PaginationResponse;
  };
}

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class CategoryApiService {
  private async fetchApi<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Get all categories with pagination and filtering
  async getCategories(params: CategoryQuery = {}): Promise<CategoriesResponse> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/categories${queryString ? `?${queryString}` : ''}`;
    
    return this.fetchApi<CategoriesResponse>(endpoint);
  }

  // Get category tree structure
  async getCategoryTree(language: 'ar' | 'fr' | 'en' = 'fr'): Promise<CategoryTreeResponse> {
    return this.fetchApi<CategoryTreeResponse>(`/categories/tree?language=${language}`);
  }

  // Get single category by ID
  async getCategoryById(id: string): Promise<CategoryResponse> {
    return this.fetchApi<CategoryResponse>(`/categories/${id}`);
  }

  // Get products by category
  async getCategoryProducts(
    id: string, 
    params: PaginationParams = {}
  ): Promise<CategoryProductsResponse> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/categories/${id}/products${queryString ? `?${queryString}` : ''}`;
    
    return this.fetchApi<CategoryProductsResponse>(endpoint);
  }

  // Get root categories (categories without parent)
  async getRootCategories(language: 'ar' | 'fr' | 'en' = 'fr'): Promise<CategoriesResponse> {
    return this.getCategories({
      parentCategory: null,
      isActive: true,
      language,
      sort: 'sortOrder',
      order: 'asc'
    });
  }

  // Get subcategories of a parent category
  async getSubcategories(
    parentId: string, 
    language: 'ar' | 'fr' | 'en' = 'fr'
  ): Promise<CategoriesResponse> {
    return this.getCategories({
      parentCategory: parentId,
      isActive: true,
      language,
      sort: 'sortOrder',
      order: 'asc'
    });
  }
}

// Export singleton instance
export const categoryApi = new CategoryApiService();
export default categoryApi;
