
import React, { useEffect, useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const EnhancedHeroCarousel: React.FC = () => {
  const { t, language, isRTL } = useLanguage();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const slides = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=1920&q=80",
      title: t('hero.title'),
      subtitle: t('hero.subtitle'),
      cta: t('hero.cta'),
      promo: t('hero.promo'),
      bgGradient: 'from-sand/90 via-sand/70 to-transparent',
      textColor: 'text-white'
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'زيت أركان نقي' : language === 'fr' ? 'Huile d\'Argan Pure' : 'Pure Argan Oil',
      subtitle: language === 'ar' ? 'من قلب المغرب، جودة استثنائية' : language === 'fr' ? 'Du cœur du Maroc, qualité exceptionnelle' : 'From the heart of Morocco, exceptional quality',
      cta: t('hero.cta'),
      bgGradient: 'from-olive/90 via-olive/70 to-transparent',
      textColor: 'text-white'
    },
    {
      id: 3,
      image: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'عسل طبيعي صافي' : language === 'fr' ? 'Miel Naturel Pur' : 'Pure Natural Honey',
      subtitle: language === 'ar' ? 'حلاوة الطبيعة في كل قطرة' : language === 'fr' ? 'La douceur de la nature dans chaque goutte' : 'Nature\'s sweetness in every drop',
      cta: t('hero.cta'),
      bgGradient: 'from-golden/90 via-golden/70 to-transparent',
      textColor: 'text-white'
    },
    {
      id: 4,
      image: "https://images.unsplash.com/photo-1599894630746-8b8e0f2d4d00?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'أملو تقليدي' : language === 'fr' ? 'Amlou Traditionnel' : 'Traditional Amlou',
      subtitle: language === 'ar' ? 'وصفة أصيلة بنكهة المغرب' : language === 'fr' ? 'Recette authentique au goût du Maroc' : 'Authentic recipe with Moroccan flavor',
      cta: t('hero.cta'),
      bgGradient: 'from-saffron/90 via-saffron/70 to-transparent',
      textColor: 'text-white'
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isPlaying, slides.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  return (
    <section className="relative h-[80vh] min-h-[600px] overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.7, ease: "easeInOut" }}
          className="absolute inset-0"
        >
          {/* Background Image */}
          <div 
            className="absolute inset-0 bg-cover bg-center transition-transform duration-700"
            style={{ backgroundImage: `url(${slides[currentSlide].image})` }}
          />
          
          {/* Overlay */}
          <div className={`absolute inset-0 bg-gradient-to-r ${slides[currentSlide].bgGradient}`} />
          
          {/* Content */}
          <div className="absolute inset-0 flex items-center">
            <div className="container-custom">
              <motion.div 
                className={`max-w-2xl ${isRTL ? 'mr-auto text-right' : 'ml-auto text-left'}`}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                <motion.h1 
                  className={`text-5xl md:text-6xl lg:text-7xl font-bold mb-6 ${slides[currentSlide].textColor}
                  ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
                  initial={{ opacity: 0, x: isRTL ? 50 : -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  {slides[currentSlide].title}
                </motion.h1>
                
                <motion.p 
                  className={`text-xl md:text-2xl mb-8 ${slides[currentSlide].textColor}/90
                  ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                  initial={{ opacity: 0, x: isRTL ? 50 : -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.6 }}
                >
                  {slides[currentSlide].subtitle}
                </motion.p>
                
                {slides[currentSlide].promo && (
                  <motion.div 
                    className="bg-white/20 backdrop-blur-sm px-6 py-3 rounded-lg inline-block mb-8"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: 0.8 }}
                  >
                    <p className={`text-white font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {slides[currentSlide].promo}
                    </p>
                  </motion.div>
                )}
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                >
                  <Button 
                    size="lg" 
                    className={`bg-white text-olive hover:bg-olive hover:text-white transition-all duration-300 transform hover:scale-105
                    ${language === 'ar' ? 'font-arabic' : 'font-body'} px-8 py-4 text-lg`}
                  >
                    {slides[currentSlide].cta}
                  </Button>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Arrows */}
      <button
        onClick={prevSlide}
        className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'right-6' : 'left-6'} bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full p-3 transition-all duration-300 hover:scale-110`}
      >
        {isRTL ? <ChevronRight className="h-6 w-6 text-white" /> : <ChevronLeft className="h-6 w-6 text-white" />}
      </button>
      
      <button
        onClick={nextSlide}
        className={`absolute top-1/2 transform -translate-y-1/2 ${isRTL ? 'left-6' : 'right-6'} bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full p-3 transition-all duration-300 hover:scale-110`}
      >
        {isRTL ? <ChevronLeft className="h-6 w-6 text-white" /> : <ChevronRight className="h-6 w-6 text-white" />}
      </button>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
        {slides.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
          />
        ))}
      </div>

      {/* Play/Pause Button */}
      <button
        onClick={() => setIsPlaying(!isPlaying)}
        className="absolute bottom-6 right-6 bg-white/20 backdrop-blur-sm hover:bg-white/30 rounded-full p-2 transition-all duration-300"
      >
        {isPlaying ? (
          <Pause className="h-4 w-4 text-white" />
        ) : (
          <Play className="h-4 w-4 text-white" />
        )}
      </button>
    </section>
  );
};

export default EnhancedHeroCarousel;
