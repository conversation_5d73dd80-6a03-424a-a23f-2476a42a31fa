
import React, { useState } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { ChevronDown } from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

type NavItemSubmenu = {
  ar: string;
  fr: string;
  en: string;
};

type NavItem = {
  id: number;
  ar: string;
  fr: string;
  en: string;
  dropdown?: boolean;
  submenu?: NavItemSubmenu[];
  badge?: string;
  href?: string;
};

const navItems: NavItem[] = [
  {
    id: 1,
    ar: "الرئيسية",
    fr: "Accueil",
    en: "Home",
    dropdown: false,
    href: "/"
  },
  {
    id: 2,
    ar: "زيت أركان",
    fr: "Huile d'Argan",
    en: "Argan Oil",
    dropdown: false,
    href: "/argan"
  },
  {
    id: 3,
    ar: "عسل",
    fr: "Miel",
    en: "Honey",
    dropdown: false,
    href: "/honey"
  },
  {
    id: 4,
    ar: "أملو",
    fr: "Amlou",
    en: "Amlou",
    dropdown: false,
    href: "/amlou"
  },
  {
    id: 5,
    ar: "العروض",
    fr: "Promotions",
    en: "Promotions",
    dropdown: false,
    href: "/deals"
  },
  {
    id: 6,
    ar: "من نحن",
    fr: "À Propos",
    en: "About Us",
    dropdown: false,
    href: "/about"
  },
  {
    id: 7,
    ar: "اتصل بنا",
    fr: "Contact",
    en: "Contact Us",
    dropdown: false,
    href: "/contact"
  },
  {
    id: 8,
    ar: "سياسة الخصوصية",
    fr: "Confidentialité",
    en: "Privacy Policy",
    dropdown: false,
    href: "/privacy"
  }
];

const MainNavigationMenu: React.FC = () => {
  const { language, isRTL } = useI18n();
  
  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList className={`space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
        {navItems.map((item) => (
          <NavigationMenuItem key={item.id}>
            {item.dropdown ? (
              <>
                <NavigationMenuTrigger className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive flex items-center`}>
                  {item[language as keyof Pick<NavItem, 'ar' | 'fr' | 'en'>]}
                  {item.badge && (
                    <span className="ml-2 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {item.badge}
                    </span>
                  )}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[200px] gap-3 p-4">
                    {item.submenu?.map((subitem, index) => (
                      <li key={index} className="row-span-1">
                        <NavigationMenuLink asChild>
                          <a
                            href={`${item.href}/${index + 1}`}
                            className={`block select-none rounded-md p-3 hover:bg-accent hover:text-accent-foreground ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
                          >
                            {subitem[language as keyof NavItemSubmenu]}
                          </a>
                        </NavigationMenuLink>
                      </li>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </>
            ) : (
              <NavigationMenuLink asChild>
                <a 
                  href={item.href}
                  className={`${navigationMenuTriggerStyle()} ${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive flex items-center`}
                >
                  {item[language as keyof Pick<NavItem, 'ar' | 'fr' | 'en'>]}
                  {item.badge && (
                    <span className="ml-2 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {item.badge}
                    </span>
                  )}
                </a>
              </NavigationMenuLink>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default MainNavigationMenu;
