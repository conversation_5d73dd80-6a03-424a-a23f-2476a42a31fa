
import React, { useState } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { useCategories } from '@/hooks/useCategories';
import { ChevronDown } from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";



const MainNavigationMenu: React.FC = () => {
  const { language, isRTL } = useI18n();
  const { categories, loading } = useCategories();

  // Static navigation items (non-category pages)
  const staticNavItems = [
    {
      id: 'home',
      ar: "الرئيسية",
      fr: "Accueil",
      en: "Home",
      href: "/"
    },
    {
      id: 'deals',
      ar: "العروض",
      fr: "Promotions",
      en: "Promotions",
      href: "/deals"
    },
    {
      id: 'about',
      ar: "من نحن",
      fr: "À Propos",
      en: "About Us",
      href: "/about"
    },
    {
      id: 'contact',
      ar: "اتصل بنا",
      fr: "Contact",
      en: "Contact Us",
      href: "/contact"
    }
  ];

  // Combine static items with dynamic categories
  const allNavItems = [
    staticNavItems[0], // Home
    ...categories.map(category => ({
      id: category._id,
      ar: category.name.ar,
      fr: category.name.fr,
      en: category.name.en,
      href: `/category/${category.slug[language]}`
    })),
    ...staticNavItems.slice(1) // Deals, About, Contact
  ];
  
  if (loading) {
    return (
      <NavigationMenu className="hidden md:flex">
        <NavigationMenuList className={`space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
          <div className="animate-pulse flex space-x-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-6 bg-gray-200 rounded w-20"></div>
            ))}
          </div>
        </NavigationMenuList>
      </NavigationMenu>
    );
  }

  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList className={`space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
        {allNavItems.map((item) => (
          <NavigationMenuItem key={item.id}>
            <NavigationMenuLink asChild>
              <a
                href={item.href}
                className={`${navigationMenuTriggerStyle()} ${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive flex items-center`}
              >
                {item[language as keyof Pick<typeof item, 'ar' | 'fr' | 'en'>]}
              </a>
            </NavigationMenuLink>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};


export default MainNavigationMenu;
