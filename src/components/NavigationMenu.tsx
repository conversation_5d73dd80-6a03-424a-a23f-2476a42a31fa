
import React, { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { ChevronDown } from 'lucide-react';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";

type NavItemSubmenu = {
  ar: string;
  fr: string;
  en: string;
};

type NavItem = {
  id: number;
  ar: string;
  fr: string;
  en: string;
  dropdown?: boolean;
  submenu?: NavItemSubmenu[];
  badge?: string;
  href?: string;
};

const navItems: NavItem[] = [
  {
    id: 1,
    ar: "الرئيسية", 
    fr: "Accueil", 
    en: "Home",
    dropdown: false,
    href: "/"
  },
  {
    id: 2,
    ar: "زيت أركان", 
    fr: "Huile d'Argan", 
    en: "Argan Oil",
    dropdown: true,
    submenu: [
      { ar: "زيت غذائي", fr: "Culinaire", en: "Culinary" },
      { ar: "زيت تجميلي", fr: "Cosmétique", en: "Cosmetic" }
    ],
    href: "/argan"
  },
  {
    id: 3,
    ar: "أملو", 
    fr: "Amlou", 
    en: "Amlou",
    dropdown: false,
    href: "/amlou"
  },
  {
    id: 4,
    ar: "عسل", 
    fr: "Miel", 
    en: "Honey",
    dropdown: false,
    href: "/honey"
  },
  {
    id: 5,
    ar: "العروض", 
    fr: "Promotions", 
    en: "Deals",
    badge: "HOT",
    href: "/deals"
  }
];

const MainNavigationMenu: React.FC = () => {
  const { language, isRTL } = useLanguage();
  
  return (
    <NavigationMenu className="hidden md:flex">
      <NavigationMenuList className={`space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
        {navItems.map((item) => (
          <NavigationMenuItem key={item.id}>
            {item.dropdown ? (
              <>
                <NavigationMenuTrigger className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive flex items-center`}>
                  {item[language as keyof Pick<NavItem, 'ar' | 'fr' | 'en'>]}
                  {item.badge && (
                    <span className="ml-2 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {item.badge}
                    </span>
                  )}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid w-[200px] gap-3 p-4">
                    {item.submenu?.map((subitem, index) => (
                      <li key={index} className="row-span-1">
                        <NavigationMenuLink asChild>
                          <a
                            href={`${item.href}/${index + 1}`}
                            className={`block select-none rounded-md p-3 hover:bg-accent hover:text-accent-foreground ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
                          >
                            {subitem[language as keyof NavItemSubmenu]}
                          </a>
                        </NavigationMenuLink>
                      </li>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </>
            ) : (
              <NavigationMenuLink asChild>
                <a 
                  href={item.href}
                  className={`${navigationMenuTriggerStyle()} ${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive flex items-center`}
                >
                  {item[language as keyof Pick<NavItem, 'ar' | 'fr' | 'en'>]}
                  {item.badge && (
                    <span className="ml-2 bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                      {item.badge}
                    </span>
                  )}
                </a>
              </NavigationMenuLink>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
};

export default MainNavigationMenu;
