import React, { useState, useEffect } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Filter, X, RotateCcw, ChevronDown, ChevronUp, DollarSign, Package, Star, Tag, Sparkles } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export interface FilterOptions {
  priceRange: [number, number];
  categories: string[];
  availability: 'all' | 'inStock' | 'outOfStock' | 'onSale';
  organic: boolean;
  tags: string[];
}

interface AdvancedFiltersProps {
  categoryId: string;
  availableCategories?: string[];
  availableTags?: string[];
  priceRange: [number, number];
  onFiltersChange: (filters: FilterOptions) => void;
  className?: string;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  categoryId,
  availableCategories = [],
  availableTags = [],
  priceRange,
  onFiltersChange,
  className = '',
}) => {
  const { t, isRTL } = useI18n();
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    priceRange: priceRange,
    categories: [],
    availability: 'all',
    organic: false,
    tags: [],
  });

  // Update filters when they change
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const handlePriceRangeChange = (value: number[]) => {
    setFilters(prev => ({
      ...prev,
      priceRange: [value[0], value[1]],
    }));
  };

  const handleCategoryToggle = (category: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category],
    }));
  };

  const handleTagToggle = (tag: string) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const handleAvailabilityChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      availability: value as FilterOptions['availability'],
    }));
  };

  const handleOrganicToggle = (checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      organic: checked,
    }));
  };

  const clearFilters = () => {
    setFilters({
      priceRange: priceRange,
      categories: [],
      availability: 'all',
      organic: false,
      tags: [],
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.priceRange[0] !== priceRange[0] || filters.priceRange[1] !== priceRange[1]) count++;
    if (filters.categories.length > 0) count++;
    if (filters.availability !== 'all') count++;
    if (filters.organic) count++;
    if (filters.tags.length > 0) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={`w-full shadow-lg border-0 bg-gradient-to-br from-white to-gray-50/50 ${className}`}>
      <CardHeader className="pb-4 bg-gradient-to-r from-olive/5 to-sand/5 rounded-t-lg">
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <CardTitle className={`flex items-center gap-3 text-lg font-semibold ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="p-2 bg-olive/10 rounded-full">
              <Filter className="h-5 w-5 text-olive" />
            </div>
            <span className="text-gray-800">{t('filters.title', 'Filters')}</span>
            {activeFiltersCount > 0 && (
              <Badge variant="default" className="bg-olive text-white hover:bg-olive/90 ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {activeFiltersCount > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
              >
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className={`text-gray-600 hover:text-olive hover:border-olive transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  {t('filters.clearFilters')}
                </Button>
              </motion.div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="md:hidden hover:bg-olive/10"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4 text-olive" />
              ) : (
                <ChevronDown className="h-4 w-4 text-olive" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ 
            height: isExpanded || window.innerWidth >= 768 ? 'auto' : 0, 
            opacity: isExpanded || window.innerWidth >= 768 ? 1 : 0 
          }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="overflow-hidden"
        >
          <CardContent className="space-y-8 p-6">
            {/* Price Range Filter */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="p-1.5 bg-green-100 rounded-lg">
                  <DollarSign className="h-4 w-4 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800">{t('filters.priceRange')}</h4>
              </div>
              <div className="bg-gray-50/50 rounded-xl p-4 space-y-4">
                <Slider
                  value={filters.priceRange}
                  onValueChange={handlePriceRangeChange}
                  max={priceRange[1]}
                  min={priceRange[0]}
                  step={10}
                  className="w-full"
                />
                <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="bg-white px-3 py-1.5 rounded-lg border shadow-sm">
                    <span className="text-sm font-medium text-gray-700">{filters.priceRange[0]} DH</span>
                  </div>
                  <div className="flex-1 mx-3">
                    <div className="h-px bg-gradient-to-r from-olive/20 via-olive/40 to-olive/20"></div>
                  </div>
                  <div className="bg-white px-3 py-1.5 rounded-lg border shadow-sm">
                    <span className="text-sm font-medium text-gray-700">{filters.priceRange[1]} DH</span>
                  </div>
                </div>
              </div>
            </motion.div>

            <Separator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent" />

            {/* Availability Filter */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="p-1.5 bg-blue-100 rounded-lg">
                  <Package className="h-4 w-4 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-800">{t('filters.availability')}</h4>
              </div>
              <div className="bg-gray-50/50 rounded-xl p-4">
                <Select value={filters.availability} onValueChange={handleAvailabilityChange}>
                  <SelectTrigger className="bg-white border-gray-200 hover:border-olive transition-colors">
                    <SelectValue placeholder={t('filters.availability')} />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 shadow-lg">
                    <SelectItem value="all" className="hover:bg-olive/5">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        {t('common.all')}
                      </div>
                    </SelectItem>
                    <SelectItem value="inStock" className="hover:bg-green-50">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        {t('filters.inStock')}
                      </div>
                    </SelectItem>
                    <SelectItem value="outOfStock" className="hover:bg-red-50">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        {t('filters.outOfStock')}
                      </div>
                    </SelectItem>
                    <SelectItem value="onSale" className="hover:bg-orange-50">
                      <div className="flex items-center gap-2">
                        <Sparkles className="w-3 h-3 text-orange-500" />
                        {t('filters.onSale')}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </motion.div>

            <Separator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent" />

            {/* Organic Filter */}
            <motion.div
              className="space-y-4"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="p-1.5 bg-green-100 rounded-lg">
                  <Star className="h-4 w-4 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-800">{t('filters.organic')}</h4>
              </div>
              <div className="bg-gray-50/50 rounded-xl p-4">
                <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                  <Checkbox
                    id="organic"
                    checked={filters.organic}
                    onCheckedChange={handleOrganicToggle}
                    className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                  />
                  <label htmlFor="organic" className="text-sm font-medium text-gray-700 cursor-pointer">
                    {t('filters.organic')}
                  </label>
                  {filters.organic && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="ml-auto"
                    >
                      <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                        ✓ {t('common.selected', 'Selected')}
                      </Badge>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>

            {/* Category-specific Tags */}
            {availableTags.length > 0 && (
              <>
                <Separator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent" />
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="p-1.5 bg-purple-100 rounded-lg">
                      <Tag className="h-4 w-4 text-purple-600" />
                    </div>
                    <h4 className="font-semibold text-gray-800">{t('categories.' + categoryId)}</h4>
                  </div>
                  <div className="bg-gray-50/50 rounded-xl p-4">
                    <div className="flex flex-wrap gap-2">
                      {availableTags.map((tag, index) => (
                        <motion.div
                          key={tag}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.1 * index }}
                        >
                          <Button
                            variant={filters.tags.includes(tag) ? "default" : "outline"}
                            size="sm"
                            onClick={() => handleTagToggle(tag)}
                            className={`text-xs transition-all duration-200 ${
                              filters.tags.includes(tag)
                                ? "bg-olive text-white hover:bg-olive/90 shadow-md"
                                : "bg-white hover:bg-olive/5 hover:border-olive border-gray-200"
                            }`}
                          >
                            {filters.tags.includes(tag) && (
                              <span className="mr-1">✓</span>
                            )}
                            {t(`filters.${categoryId}.${tag}`)}
                          </Button>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </>
            )}

            {/* Additional Categories (for Promotions page) */}
            {availableCategories.length > 0 && (
              <>
                <Separator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent" />
                <motion.div
                  className="space-y-4"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="p-1.5 bg-orange-100 rounded-lg">
                      <Package className="h-4 w-4 text-orange-600" />
                    </div>
                    <h4 className="font-semibold text-gray-800">{t('filters.category')}</h4>
                  </div>
                  <div className="bg-gray-50/50 rounded-xl p-4 space-y-3">
                    {availableCategories.map((category, index) => (
                      <motion.div
                        key={category}
                        className={`flex items-center space-x-3 p-2 rounded-lg hover:bg-white transition-colors ${isRTL ? 'space-x-reverse' : ''}`}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.1 * index }}
                      >
                        <Checkbox
                          id={category}
                          checked={filters.categories.includes(category)}
                          onCheckedChange={() => handleCategoryToggle(category)}
                          className="data-[state=checked]:bg-olive data-[state=checked]:border-olive"
                        />
                        <label htmlFor={category} className="text-sm font-medium text-gray-700 cursor-pointer flex-1">
                          {t('categories.' + category)}
                        </label>
                        {filters.categories.includes(category) && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                          >
                            <Badge variant="secondary" className="bg-olive/10 text-olive border-olive/20">
                              ✓
                            </Badge>
                          </motion.div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </>
            )}
          </CardContent>
        </motion.div>
      </AnimatePresence>
    </Card>
  );
};

export default AdvancedFilters;
