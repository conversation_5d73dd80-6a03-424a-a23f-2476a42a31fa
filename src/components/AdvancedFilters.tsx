import React, { useState, useEffect } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Filter, X, RotateCcw } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export interface FilterOptions {
  priceRange: [number, number];
  categories: string[];
  availability: 'all' | 'inStock' | 'outOfStock' | 'onSale';
  organic: boolean;
  tags: string[];
}

interface AdvancedFiltersProps {
  categoryId: string;
  availableCategories?: string[];
  availableTags?: string[];
  priceRange: [number, number];
  onFiltersChange: (filters: FilterOptions) => void;
  className?: string;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  categoryId,
  availableCategories = [],
  availableTags = [],
  priceRange,
  onFiltersChange,
  className = '',
}) => {
  const { t, isRTL } = useI18n();
  const [isExpanded, setIsExpanded] = useState(false);
  const [filters, setFilters] = useState<FilterOptions>({
    priceRange: priceRange,
    categories: [],
    availability: 'all',
    organic: false,
    tags: [],
  });

  // Update filters when they change
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const handlePriceRangeChange = (value: number[]) => {
    setFilters(prev => ({
      ...prev,
      priceRange: [value[0], value[1]],
    }));
  };

  const handleCategoryToggle = (category: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category],
    }));
  };

  const handleTagToggle = (tag: string) => {
    setFilters(prev => ({
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag],
    }));
  };

  const handleAvailabilityChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      availability: value as FilterOptions['availability'],
    }));
  };

  const handleOrganicToggle = (checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      organic: checked,
    }));
  };

  const clearFilters = () => {
    setFilters({
      priceRange: priceRange,
      categories: [],
      availability: 'all',
      organic: false,
      tags: [],
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.priceRange[0] !== priceRange[0] || filters.priceRange[1] !== priceRange[1]) count++;
    if (filters.categories.length > 0) count++;
    if (filters.availability !== 'all') count++;
    if (filters.organic) count++;
    if (filters.tags.length > 0) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <CardTitle className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Filter className="h-5 w-5" />
            {t('filters.title', 'Filters')}
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className={`text-muted-foreground hover:text-foreground ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                {t('filters.clearFilters')}
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="md:hidden"
            >
              {isExpanded ? <X className="h-4 w-4" /> : <Filter className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>

      <AnimatePresence>
        <motion.div
          initial={{ height: 0, opacity: 0 }}
          animate={{ 
            height: isExpanded || window.innerWidth >= 768 ? 'auto' : 0, 
            opacity: isExpanded || window.innerWidth >= 768 ? 1 : 0 
          }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="overflow-hidden"
        >
          <CardContent className="space-y-6">
            {/* Price Range Filter */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">{t('filters.priceRange')}</h4>
              <div className="px-2">
                <Slider
                  value={filters.priceRange}
                  onValueChange={handlePriceRangeChange}
                  max={priceRange[1]}
                  min={priceRange[0]}
                  step={10}
                  className="w-full"
                />
                <div className={`flex justify-between text-sm text-muted-foreground mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span>{filters.priceRange[0]} DH</span>
                  <span>{filters.priceRange[1]} DH</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Availability Filter */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">{t('filters.availability')}</h4>
              <Select value={filters.availability} onValueChange={handleAvailabilityChange}>
                <SelectTrigger>
                  <SelectValue placeholder={t('filters.availability')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="inStock">{t('filters.inStock')}</SelectItem>
                  <SelectItem value="outOfStock">{t('filters.outOfStock')}</SelectItem>
                  <SelectItem value="onSale">{t('filters.onSale')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Separator />

            {/* Organic Filter */}
            <div className="space-y-3">
              <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                <Checkbox
                  id="organic"
                  checked={filters.organic}
                  onCheckedChange={handleOrganicToggle}
                />
                <label htmlFor="organic" className="text-sm font-medium">
                  {t('filters.organic')}
                </label>
              </div>
            </div>

            {/* Category-specific Tags */}
            {availableTags.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">{t('categories.' + categoryId)}</h4>
                  <div className="flex flex-wrap gap-2">
                    {availableTags.map((tag) => (
                      <Button
                        key={tag}
                        variant={filters.tags.includes(tag) ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleTagToggle(tag)}
                        className="text-xs"
                      >
                        {t(`filters.${categoryId}.${tag}`)}
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Additional Categories (for Promotions page) */}
            {availableCategories.length > 0 && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">{t('filters.category')}</h4>
                  <div className="space-y-2">
                    {availableCategories.map((category) => (
                      <div key={category} className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
                        <Checkbox
                          id={category}
                          checked={filters.categories.includes(category)}
                          onCheckedChange={() => handleCategoryToggle(category)}
                        />
                        <label htmlFor={category} className="text-sm">
                          {t('categories.' + category)}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </motion.div>
      </AnimatePresence>
    </Card>
  );
};

export default AdvancedFilters;
