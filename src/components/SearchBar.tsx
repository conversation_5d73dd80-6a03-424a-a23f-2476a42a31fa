
import React, { useState, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { products } from '@/data/products';
import ProductCard from './ProductCard';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const SearchBar: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const filteredProducts = useMemo(() => {
    if (!searchTerm.trim()) return [];
    
    return products.filter(product =>
      product.title[language as keyof typeof product.title]
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      product.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, language]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const clearSearch = () => {
    setSearchTerm('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="hidden md:flex">
          <Search className="h-5 w-5" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className={`${language === 'ar' ? 'font-arabic text-right' : 'font-title'}`}>
            {language === 'ar' 
              ? 'البحث عن المنتجات' 
              : language === 'fr' 
                ? 'Rechercher des produits' 
                : 'Search Products'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="relative">
            <Search className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4`} />
            <Input
              type="text"
              placeholder={
                language === 'ar' 
                  ? 'البحث عن المنتجات...' 
                  : language === 'fr' 
                    ? 'Rechercher des produits...' 
                    : 'Search products...'
              }
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className={`${isRTL ? 'pr-10 pl-12' : 'pl-10 pr-12'} ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}
              autoFocus
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearSearch}
                className={`absolute ${isRTL ? 'left-2' : 'right-2'} top-1/2 transform -translate-y-1/2 h-6 w-6 p-0`}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          <div className="max-h-96 overflow-y-auto">
            <AnimatePresence>
              {searchTerm && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="space-y-4"
                >
                  <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
                    {language === 'ar' 
                      ? `${filteredProducts.length} نتيجة` 
                      : language === 'fr' 
                        ? `${filteredProducts.length} résultat${filteredProducts.length > 1 ? 's' : ''}` 
                        : `${filteredProducts.length} result${filteredProducts.length !== 1 ? 's' : ''}`}
                  </p>
                  
                  {filteredProducts.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {filteredProducts.slice(0, 6).map((product) => (
                        <motion.div
                          key={product.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.2 }}
                          onClick={() => setIsOpen(false)}
                        >
                          <ProductCard
                            id={product.id}
                            title={product.title[language as keyof typeof product.title]}
                            price={product.price}
                            originalPrice={product.originalPrice}
                            image={product.image}
                            category={product.category}
                            isOrganic={product.isOrganic}
                            discountPercentage={product.discountPercentage}
                          />
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className={`text-center py-8 text-gray-500 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {language === 'ar' 
                        ? 'لم يتم العثور على منتجات' 
                        : language === 'fr' 
                          ? 'Aucun produit trouvé' 
                          : 'No products found'}
                    </div>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SearchBar;
