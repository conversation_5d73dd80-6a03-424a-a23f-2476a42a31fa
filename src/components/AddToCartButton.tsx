
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { ShoppingCart, Check } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCart } from '@/contexts/CartContext';
import { toast } from 'sonner';

interface AddToCartButtonProps {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  discountPercentage?: number;
  totalPrice: number;
  selectedQuantity: number;
  selectedAttributes: Record<string, string>;
  itemVariants: any;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  id,
  title,
  price,
  originalPrice,
  image,
  category,
  discountPercentage,
  totalPrice,
  selectedQuantity,
  selectedAttributes,
  itemVariants
}) => {
  const { language } = useLanguage();
  const { addToCart } = useCart();
  const [isAdding, setIsAdding] = useState(false);

  const handleAddToCart = () => {
    setIsAdding(true);
    
    addToCart({
      id,
      title,
      price: totalPrice,
      originalPrice,
      image,
      category,
      quantity: selectedQuantity,
      attributes: selectedAttributes,
      discountPercentage
    });
    
    setTimeout(() => {
      setIsAdding(false);
      toast.success(
        language === 'ar' 
          ? `تمت إضافة ${selectedQuantity} قطعة إلى السلة` 
          : language === 'fr' 
            ? `${selectedQuantity} produit(s) ajouté(s) au panier` 
            : `${selectedQuantity} item(s) added to cart`
      );
    }, 600);
  };

  return (
    <motion.div variants={itemVariants} className="pt-4">
      <Button 
        onClick={handleAddToCart} 
        className="w-full bg-olive hover:bg-olive/90"
        size="lg"
        disabled={isAdding}
      >
        <AnimatePresence mode="wait">
          {isAdding ? (
            <motion.div
              key="adding"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              className="flex items-center"
            >
              <Check className="h-5 w-5 mr-2" />
              {language === 'ar' ? 'تمت الإضافة' : language === 'fr' ? 'Ajouté' : 'Added'}
            </motion.div>
          ) : (
            <motion.div
              key="add"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              exit={{ scale: 0 }}
              className="flex items-center"
            >
              <ShoppingCart className="h-5 w-5 mr-2" />
              {language === 'ar' ? 'أضف إلى السلة' : language === 'fr' ? 'Ajouter au panier' : 'Add to Cart'}
            </motion.div>
          )}
        </AnimatePresence>
      </Button>
    </motion.div>
  );
};

export default AddToCartButton;
