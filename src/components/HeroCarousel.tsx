
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';

const HeroCarousel: React.FC = () => {
  const { t, language, isRTL } = useLanguage();

  const slides = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=1920&q=80",
      title: t('hero.title'),
      subtitle: t('hero.subtitle'),
      cta: t('hero.cta'),
      promo: t('hero.promo'),
      bgColor: 'from-sand/80 to-sand/10'
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'منتجات طبيعية' : language === 'fr' ? 'Produits Naturels' : 'Natural Products',
      subtitle: language === 'ar' ? 'جودة تقليدية مغربية' : language === 'fr' ? 'Qualité traditionnelle marocaine' : 'Traditional Moroccan Quality',
      cta: t('hero.cta'),
      bgColor: 'from-olive/80 to-olive/10'
    },
    {
      id: 3,
      image: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'صنع يدويًا بعناية' : language === 'fr' ? 'Fait à la main avec soin' : 'Handcrafted with Care',
      subtitle: language === 'ar' ? 'التقاليد المغربية العريقة' : language === 'fr' ? 'Traditions marocaines authentiques' : 'Authentic Moroccan Traditions',
      cta: t('hero.cta'),
      bgColor: 'from-saffron/80 to-saffron/10'
    }
  ];

  return (
    <section className="relative">
      <Carousel className="w-full">
        <CarouselContent>
          {slides.map((slide) => (
            <CarouselItem key={slide.id} className="h-[70vh] min-h-[500px] relative">
              {/* Background Image */}
              <div 
                className="absolute inset-0 bg-cover bg-center" 
                style={{ backgroundImage: `url(${slide.image})` }}
              />
              
              {/* Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-r ${slide.bgColor} opacity-90`} />
              
              <div className="absolute inset-0 flex items-center">
                <div className="container-custom">
                  <div className={`max-w-xl ${isRTL ? 'mr-auto text-right' : 'ml-auto text-left'}`}>
                    <h1 
                      className={`text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4
                      ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
                    >
                      {slide.title}
                    </h1>
                    <p 
                      className={`text-xl md:text-2xl text-white/90 mb-8
                      ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                    >
                      {slide.subtitle}
                    </p>
                    {slide.promo && (
                      <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-lg inline-block mb-6">
                        <p className={`text-white font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                          {slide.promo}
                        </p>
                      </div>
                    )}
                    <Button 
                      size="lg" 
                      className={`bg-white text-olive hover:bg-olive hover:text-white
                      ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                    >
                      {slide.cta}
                    </Button>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10">
          <CarouselPrevious className="relative inset-auto" />
          <CarouselNext className="relative inset-auto" />
        </div>
      </Carousel>
    </section>
  );
};

export default HeroCarousel;
