
import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';

interface ProductInfoSectionProps {
  title: string;
  price: number;
  itemVariants: any;
}

const ProductInfoSection: React.FC<ProductInfoSectionProps> = ({
  title,
  price,
  itemVariants
}) => {
  const { language, isRTL } = useLanguage();

  return (
    <motion.div 
      className={`space-y-6 ${isRTL ? 'rtl:pr-8' : 'ltr:pl-8'}`}
      variants={itemVariants}
    >
      <div>
        <h1 className={`text-3xl font-bold mb-2 text-olive ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
          {title}
        </h1>
        
        <div className="text-sm text-gray-600 mb-4">
          {language === 'ar' ? 'السعر الأساسي:' : language === 'fr' ? 'Prix de base:' : 'Base price:'} {price.toFixed(2)} MAD / 1kg
        </div>
      </div>
    </motion.div>
  );
};

export default ProductInfoSection;
