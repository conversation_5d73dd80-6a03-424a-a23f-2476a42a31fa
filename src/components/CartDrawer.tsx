
import React from 'react';
import {
  Drawer,
  DrawerClose,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>erTit<PERSON>,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { ShoppingCart, X, Trash2, Plus, Minus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCart } from '@/contexts/CartContext';
import { motion } from 'framer-motion';

type CartDrawerProps = {
  isOpen: boolean;
  onClose: () => void;
};

const CartDrawer: React.FC<CartDrawerProps> = ({ isOpen, onClose }) => {
  const { language, isRTL } = useLanguage();
  const { state, updateQuantity, removeFromCart } = useCart();
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1, 
      transition: { 
        staggerChildren: 0.1
      } 
    }
  };
  
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };
  
  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className={`${isRTL ? 'rtl' : 'ltr'}`}>
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <DrawerTitle className={`${language === 'ar' ? 'font-arabic' : 'font-title'} text-xl`}>
              {language === 'ar' ? 'سلة التسوق' : language === 'fr' ? 'Panier' : 'Shopping Cart'}
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="icon">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
          <DrawerDescription className={language === 'ar' ? 'font-arabic' : 'font-body'}>
            {state.items.length === 0 
              ? (language === 'ar' 
                ? 'سلة التسوق فارغة'
                : language === 'fr'
                ? 'Votre panier est vide'
                : 'Your cart is empty') 
              : `${state.itemCount} ${language === 'ar' ? 'منتجات' : language === 'fr' ? 'produits' : 'items'}`
            }
          </DrawerDescription>
        </DrawerHeader>
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {state.items.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-10">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <ShoppingCart className="h-16 w-16 text-gray-300 mb-4" />
              </motion.div>
              <p className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-500`}>
                {language === 'ar' 
                  ? 'لم تضف أي منتجات إلى سلة التسوق بعد'
                  : language === 'fr'
                  ? 'Vous n\'avez pas encore ajouté de produits à votre panier'
                  : 'You haven\'t added any products to your cart yet'
                }
              </p>
              <Button className="mt-4" onClick={onClose}>
                {language === 'ar'
                  ? 'ابدأ التسوق'
                  : language === 'fr'
                  ? 'Commencer vos achats'
                  : 'Start Shopping'
                }
              </Button>
            </div>
          ) : (
            <motion.div
              className="space-y-4"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {state.items.map(item => (
                <motion.div 
                  key={`${item.id}-${JSON.stringify(item.attributes)}`}
                  className="flex items-center gap-4 p-3 border rounded-md hover:bg-gray-50"
                  variants={itemVariants}
                >
                  {/* Product Image */}
                  <div className="w-16 h-16 flex-shrink-0">
                    <img src={item.image} alt={item.title} className="w-full h-full object-cover rounded" />
                  </div>
                  
                  {/* Product Info */}
                  <div className="flex-grow">
                    <h4 className={`font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>{item.title}</h4>
                    
                    {/* Attributes */}
                    {item.attributes && Object.keys(item.attributes).length > 0 && (
                      <div className="flex gap-1 mt-1">
                        {Object.entries(item.attributes).map(([key, value]) => (
                          <span key={key} className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {value}
                          </span>
                        ))}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-sm text-gray-600">
                        {item.price.toFixed(2)} MAD
                      </span>
                      
                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2">
                        <button 
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-1 hover:bg-gray-100 rounded"
                          disabled={item.quantity <= 1}
                        >
                          <Minus className="h-3 w-3" />
                        </button>
                        <span className="w-5 text-center text-sm">{item.quantity}</span>
                        <button 
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-1 hover:bg-gray-100 rounded"
                        >
                          <Plus className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Remove Button */}
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="p-2 text-gray-500 hover:text-red-500"
                    onClick={() => removeFromCart(item.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </motion.button>
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
        
        {state.items.length > 0 && (
          <div className="border-t border-gray-200 p-4">
            <div className="flex justify-between mb-4">
              <span className={`font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                {language === 'ar' ? 'المجموع' : language === 'fr' ? 'Total' : 'Total'}
              </span>
              <span className="font-bold">{state.total.toFixed(2)} MAD</span>
            </div>
            
            <DrawerFooter className="px-0 pt-0">
              <Button className="w-full bg-olive hover:bg-olive/90">
                {language === 'ar'
                  ? 'الدفع'
                  : language === 'fr'
                  ? 'Passer à la caisse'
                  : 'Checkout'
                }
              </Button>
              <DrawerClose asChild>
                <Button variant="outline">
                  {language === 'ar'
                    ? 'مواصلة التسوق'
                    : language === 'fr'
                    ? 'Continuer vos achats'
                    : 'Continue Shopping'
                  }
                </Button>
              </DrawerClose>
            </DrawerFooter>
          </div>
        )}
      </DrawerContent>
    </Drawer>
  );
};

export default CartDrawer;
