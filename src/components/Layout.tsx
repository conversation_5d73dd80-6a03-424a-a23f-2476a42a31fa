
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { useReducedMotion } from '@/hooks/useReducedMotion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  // Check for reduced motion preference
  const prefersReducedMotion = useReducedMotion();

  // Page transition variants
  const pageVariants = {
    initial: prefersReducedMotion ? {
      opacity: 0
    } : {
      opacity: 0,
      y: 20,
      scale: 0.98
    },
    in: {
      opacity: 1,
      y: 0,
      scale: 1
    },
    out: prefersReducedMotion ? {
      opacity: 0
    } : {
      opacity: 0,
      y: -20,
      scale: 1.02
    }
  };

  const pageTransition = prefersReducedMotion ? {
    duration: 0.1
  } : {
    type: "tween",
    ease: "anticipate",
    duration: 0.4
  };

  return (
    <LanguageProvider>
      <div className="flex flex-col min-h-screen bg-white">
        <Header />
        <motion.main
          className="flex-grow relative overflow-hidden"
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
        >
          <motion.div
            className="w-full h-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            {children}
          </motion.div>
        </motion.main>
        <Footer />
      </div>
    </LanguageProvider>
  );
};

export default Layout;
