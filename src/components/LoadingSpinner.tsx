import React from 'react';
import { motion } from 'framer-motion';
import { useReducedMotion } from '@/hooks/useReducedMotion';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'olive' | 'golden' | 'sand' | 'white';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  color = 'olive',
  className = ''
}) => {
  const prefersReducedMotion = useReducedMotion();

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  const colorClasses = {
    olive: 'text-olive',
    golden: 'text-golden',
    sand: 'text-sand',
    white: 'text-white'
  };

  if (prefersReducedMotion) {
    return (
      <div className={`${sizeClasses[size]} ${colorClasses[color]} ${className}`}>
        <div className="w-full h-full border-2 border-current border-t-transparent rounded-full opacity-75" />
      </div>
    );
  }

  return (
    <motion.div
      className={`${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
    >
      <div className="w-full h-full border-2 border-current border-t-transparent rounded-full" />
    </motion.div>
  );
};

// Pulsing dots loader
export const PulsingDots: React.FC<{ 
  color?: 'olive' | 'golden' | 'sand' | 'white';
  className?: string;
}> = ({ color = 'olive', className = '' }) => {
  const prefersReducedMotion = useReducedMotion();

  const colorClasses = {
    olive: 'bg-olive',
    golden: 'bg-golden',
    sand: 'bg-sand',
    white: 'bg-white'
  };

  if (prefersReducedMotion) {
    return (
      <div className={`flex space-x-1 ${className}`}>
        <div className={`w-2 h-2 rounded-full ${colorClasses[color]} opacity-75`} />
        <div className={`w-2 h-2 rounded-full ${colorClasses[color]} opacity-50`} />
        <div className={`w-2 h-2 rounded-full ${colorClasses[color]} opacity-25`} />
      </div>
    );
  }

  return (
    <div className={`flex space-x-1 ${className}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-2 h-2 rounded-full ${colorClasses[color]}`}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{
            duration: 0.8,
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

// Skeleton loader for cards
export const SkeletonCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  const prefersReducedMotion = useReducedMotion();

  const shimmerVariants = {
    initial: { x: '-100%' },
    animate: { x: '100%' }
  };

  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden ${className}`}>
      <div className="relative h-48 bg-gray-200">
        {!prefersReducedMotion && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
            variants={shimmerVariants}
            initial="initial"
            animate="animate"
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        )}
      </div>
      <div className="p-4 space-y-3">
        <div className="h-4 bg-gray-200 rounded w-3/4 relative overflow-hidden">
          {!prefersReducedMotion && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              variants={shimmerVariants}
              initial="initial"
              animate="animate"
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "linear",
                delay: 0.2
              }}
            />
          )}
        </div>
        <div className="h-3 bg-gray-200 rounded w-1/2 relative overflow-hidden">
          {!prefersReducedMotion && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              variants={shimmerVariants}
              initial="initial"
              animate="animate"
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "linear",
                delay: 0.4
              }}
            />
          )}
        </div>
        <div className="h-8 bg-gray-200 rounded w-full relative overflow-hidden">
          {!prefersReducedMotion && (
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
              variants={shimmerVariants}
              initial="initial"
              animate="animate"
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "linear",
                delay: 0.6
              }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Bouncing balls loader
export const BouncingBalls: React.FC<{ 
  color?: 'olive' | 'golden' | 'sand';
  className?: string;
}> = ({ color = 'olive', className = '' }) => {
  const prefersReducedMotion = useReducedMotion();

  const colorClasses = {
    olive: 'bg-olive',
    golden: 'bg-golden',
    sand: 'bg-sand'
  };

  if (prefersReducedMotion) {
    return (
      <div className={`flex space-x-1 ${className}`}>
        <div className={`w-3 h-3 rounded-full ${colorClasses[color]}`} />
        <div className={`w-3 h-3 rounded-full ${colorClasses[color]} opacity-75`} />
        <div className={`w-3 h-3 rounded-full ${colorClasses[color]} opacity-50`} />
      </div>
    );
  }

  return (
    <div className={`flex space-x-1 items-end ${className}`}>
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`w-3 h-3 rounded-full ${colorClasses[color]}`}
          animate={{
            y: [0, -12, 0]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );
};

export default LoadingSpinner;
