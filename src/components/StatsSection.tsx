
import React, { useEffect, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Award, Users, Leaf, Heart } from 'lucide-react';

const StatsSection: React.FC = () => {
  const { language } = useLanguage();
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: true });

  const stats = [
    {
      icon: Award,
      value: 15,
      suffix: '+',
      label: {
        ar: 'سنة من الخبرة',
        fr: 'Années d\'expérience',
        en: 'Years of Experience'
      },
      color: 'text-golden'
    },
    {
      icon: Users,
      value: 500,
      suffix: '+',
      label: {
        ar: 'عميل راضي',
        fr: 'Clients satisfaits',
        en: 'Happy Customers'
      },
      color: 'text-olive'
    },
    {
      icon: Leaf,
      value: 100,
      suffix: '%',
      label: {
        ar: 'منتجات طبيعية',
        fr: 'Produits naturels',
        en: 'Natural Products'
      },
      color: 'text-green-600'
    },
    {
      icon: Heart,
      value: 50,
      suffix: '+',
      label: {
        ar: 'تعاونية نسائية',
        fr: 'Coopératives féminines',
        en: 'Women Cooperatives'
      },
      color: 'text-rose-600'
    }
  ];

  return (
    <section ref={ref} className="py-20 bg-gradient-to-r from-olive/5 to-sand/5">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className={`text-4xl font-bold text-olive mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
            {language === 'ar' 
              ? 'أرقام تتحدث عن جودتنا' 
              : language === 'fr' 
                ? 'Des chiffres qui parlent de notre qualité' 
                : 'Numbers That Speak Our Quality'}
          </h2>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center group"
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-white shadow-lg mb-6 ${stat.color} group-hover:shadow-xl transition-shadow duration-300`}
              >
                <stat.icon className="h-8 w-8" />
              </motion.div>
              
              <motion.div
                initial={{ scale: 0.5 }}
                animate={isInView ? { scale: 1 } : {}}
                transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
              >
                <AnimatedCounter
                  value={stat.value}
                  suffix={stat.suffix}
                  isInView={isInView}
                  delay={index * 0.1 + 0.5}
                />
              </motion.div>
              
              <p className={`text-gray-600 mt-2 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                {stat.label[language as keyof typeof stat.label]}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

const AnimatedCounter: React.FC<{
  value: number;
  suffix: string;
  isInView: boolean;
  delay: number;
}> = ({ value, suffix, isInView, delay }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!isInView) return;

    const timer = setTimeout(() => {
      let start = 0;
      const end = value;
      const duration = 2000;
      const increment = end / (duration / 16);

      const counter = setInterval(() => {
        start += increment;
        if (start >= end) {
          setCount(end);
          clearInterval(counter);
        } else {
          setCount(Math.floor(start));
        }
      }, 16);

      return () => clearInterval(counter);
    }, delay * 1000);

    return () => clearTimeout(timer);
  }, [isInView, value, delay]);

  return (
    <div className="text-4xl font-bold text-olive">
      {count}{suffix}
    </div>
  );
};

export default StatsSection;
