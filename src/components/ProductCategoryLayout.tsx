
import React, { useState, useMemo } from 'react';
import { useI18n } from '@/hooks/useI18n';
import ProductCard from './ProductCard';
import AdvancedFilters, { FilterOptions } from './AdvancedFilters';
import { motion } from 'framer-motion';

interface Product {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isOrganic?: boolean;
  discountPercentage?: number;
  tags?: string[];
  inStock?: boolean;
}

interface ProductCategoryLayoutProps {
  categoryId: string;
  titleKey: string;
  filterOptions?: string[];
  products: Product[];
  availableCategories?: string[];
}

const ProductCategoryLayout: React.FC<ProductCategoryLayoutProps> = ({
  categoryId,
  titleKey,
  filterOptions = [],
  products,
  availableCategories = []
}) => {
  const { t, isRTL } = useI18n();
  const [filters, setFilters] = useState<FilterOptions>({
    priceRange: [0, 1000],
    categories: [],
    availability: 'all',
    organic: false,
    tags: [],
  });

  // Calculate price range from products
  const priceRange = useMemo(() => {
    if (products.length === 0) return [0, 1000];
    const prices = products.map(p => p.price);
    return [Math.min(...prices), Math.max(...prices)];
  }, [products]);

  // Apply advanced filters
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      // Price range filter
      if (product.price < filters.priceRange[0] || product.price > filters.priceRange[1]) {
        return false;
      }

      // Category filter (for promotions page)
      if (filters.categories.length > 0 && !filters.categories.includes(product.category)) {
        return false;
      }

      // Availability filter
      if (filters.availability === 'inStock' && product.inStock === false) {
        return false;
      }
      if (filters.availability === 'outOfStock' && product.inStock !== false) {
        return false;
      }
      if (filters.availability === 'onSale' && !product.discountPercentage) {
        return false;
      }

      // Organic filter
      if (filters.organic && !product.isOrganic) {
        return false;
      }

      // Tags filter
      if (filters.tags.length > 0) {
        const hasMatchingTag = filters.tags.some(tag => product.tags?.includes(tag));
        if (!hasMatchingTag) {
          return false;
        }
      }

      return true;
    });
  }, [products, filters]);

  // Animation variants for container and items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Get category-specific styles
  const getCategoryHeaderStyle = () => {
    switch (categoryId) {
      case 'honey':
        return 'text-golden border-golden/30';
      case 'argan':
        return 'text-olive border-olive/30';
      case 'amlou':
        return 'text-sand border-sand/30';
      default:
        return 'text-primary border-primary/30';
    }
  };

  return (
    <div className={`w-full px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 ${isRTL ? 'text-right' : 'text-left'}`}>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <h1 className={`text-2xl sm:text-3xl lg:text-4xl mb-4 sm:mb-6 font-semibold ${getCategoryHeaderStyle()}`}>
          {t(`categories.${titleKey}`)}
        </h1>
      </motion.div>

      {/* Advanced Filters */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="mb-6 sm:mb-8"
      >
        <AdvancedFilters
          categoryId={categoryId}
          availableCategories={availableCategories}
          availableTags={filterOptions}
          priceRange={priceRange as [number, number]}
          onFiltersChange={setFilters}
        />
      </motion.div>
      
      {/* Products grid */}
      <motion.div
        className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-3 sm:gap-4 md:gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {filteredProducts.length > 0 ? (
          filteredProducts.map((product, index) => (
            <motion.div
              key={product.id}
              variants={itemVariants}
              transition={{ delay: index * 0.05 }}
              className="h-full"
            >
              <ProductCard
                id={product.id}
                title={product.title}
                price={product.price}
                originalPrice={product.originalPrice}
                image={product.image || '/placeholder.svg'}
                category={categoryId}
                isOrganic={product.isOrganic}
                discountPercentage={product.discountPercentage}
              />
            </motion.div>
          ))
        ) : (
          <motion.div
            className="col-span-full text-center py-16 sm:py-20"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
          >
            <div className="max-w-md mx-auto">
              <div className="text-6xl sm:text-8xl mb-4 opacity-20">🔍</div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-700 mb-2">
                {t('products.noProductsTitle', 'No Products Found')}
              </h3>
              <p className="text-sm sm:text-base text-gray-500">
                {t('products.noProductsFound', 'No products found matching your criteria')}
              </p>
            </div>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default ProductCategoryLayout;
