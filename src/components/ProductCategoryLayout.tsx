
import React, { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import ProductCard from './ProductCard';
import { motion } from 'framer-motion';

interface Product {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isOrganic?: boolean;
  discountPercentage?: number;
  tags?: string[];
}

interface ProductCategoryLayoutProps {
  categoryId: string;
  titleKey: string;
  filterOptions?: string[];
  products: Product[];
}

const ProductCategoryLayout: React.FC<ProductCategoryLayoutProps> = ({
  categoryId,
  titleKey,
  filterOptions,
  products
}) => {
  const { language, t, isRTL } = useLanguage();
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  
  // Apply filters if any are selected
  const filteredProducts = activeFilter 
    ? products.filter(p => p.tags?.includes(activeFilter))
    : products;

  // Animation variants for container and items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Get category-specific styles
  const getCategoryHeaderStyle = () => {
    switch (categoryId) {
      case 'honey':
        return 'text-golden border-golden/30';
      case 'argan':
        return 'text-olive border-olive/30';
      case 'amlou':
        return 'text-sand border-sand/30';
      default:
        return 'text-primary border-primary/30';
    }
  };

  return (
    <div className={`container-custom py-8 ${isRTL ? 'text-right' : 'text-left'}`}>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <h1 className={`text-3xl mb-6 font-semibold ${language === 'ar' ? 'font-arabic' : 'font-title'} ${getCategoryHeaderStyle()}`}>
          {t(`categories.${titleKey}`)}
        </h1>
      </motion.div>
      
      {/* Filter options */}
      {filterOptions && filterOptions.length > 0 && (
        <motion.div 
          className={`flex flex-wrap gap-2 mb-8 ${isRTL ? 'justify-end' : 'justify-start'}`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <button
            className={`px-4 py-2 rounded-full border transition-all ${activeFilter === null ? 'bg-gray-100 border-gray-400' : 'border-gray-300'}`}
            onClick={() => setActiveFilter(null)}
          >
            {language === 'ar' ? 'الكل' : language === 'fr' ? 'Tous' : 'All'}
          </button>
          
          {filterOptions.map(option => (
            <motion.button
              key={option}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`px-4 py-2 rounded-full border transition-all ${
                activeFilter === option 
                  ? categoryId === 'honey'
                    ? 'bg-golden/10 border-golden text-golden'
                    : categoryId === 'argan'
                      ? 'bg-olive/10 border-olive text-olive'
                      : categoryId === 'amlou'
                        ? 'bg-sand/10 border-sand text-sand'
                        : 'bg-primary/10 border-primary text-primary'
                  : 'border-gray-300'
              }`}
              onClick={() => setActiveFilter(option)}
            >
              {t(`filters.${categoryId}.${option}`)}
            </motion.button>
          ))}
        </motion.div>
      )}
      
      {/* Products grid */}
      <motion.div 
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {filteredProducts.length > 0 ? (
          filteredProducts.map(product => (
            <motion.div key={product.id} variants={itemVariants}>
              <ProductCard
                id={product.id}
                title={product.title}
                price={product.price}
                originalPrice={product.originalPrice}
                image={product.image || '/placeholder.svg'}
                category={categoryId}
                isOrganic={product.isOrganic}
                discountPercentage={product.discountPercentage}
              />
            </motion.div>
          ))
        ) : (
          <motion.div 
            className={`col-span-full text-center py-12 text-gray-500 ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            {language === 'ar' 
              ? 'لا توجد منتجات متاحة' 
              : language === 'fr' 
                ? 'Aucun produit disponible' 
                : 'No products available'}
          </motion.div>
        )}
      </motion.div>
    </div>
  );
};

export default ProductCategoryLayout;
