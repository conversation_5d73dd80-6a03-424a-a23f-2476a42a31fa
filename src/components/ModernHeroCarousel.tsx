import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useI18n } from '@/hooks/useI18n';
import { useReducedMotion, getHoverAnimation } from '@/hooks/useReducedMotion';
import { useCart } from '@/contexts/CartContext';
import { useCartToast } from '@/components/CartToast';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight, Play, Pause, Star, ShoppingCart, ArrowRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

interface SlideProduct {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  discountPercentage?: number;
}

interface Slide {
  id: number;
  image: string;
  title: string;
  subtitle: string;
  description: string;
  cta: string;
  ctaSecondary?: string;
  promo?: string;
  bgGradient: string;
  textColor: string;
  features: string[];
  rating?: number;
  discount?: string;
  product: SlideProduct;
  navigationPath: string;
}

const ModernHeroCarousel: React.FC = () => {
  const { t, language, isRTL } = useI18n();
  const prefersReducedMotion = useReducedMotion();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { showAddToCartToast } = useCartToast();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const slides: Slide[] = [
    {
      id: 1,
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=1920&q=80",
      title: t('hero.title'),
      subtitle: t('hero.subtitle'),
      description: t('hero.description'),
      cta: t('hero.cta'),
      ctaSecondary: t('hero.ctaSecondary'),
      promo: t('hero.promo'),
      bgGradient: 'from-sand/95 via-sand/80 to-sand/60',
      textColor: 'text-white',
      features: [t('hero.features.organic'), t('hero.features.natural'), t('hero.features.handmade')],
      rating: 4.9,
      discount: '20%',
      product: {
        id: 'honey_1',
        title: language === 'ar' ? 'عسل الزعتر' : language === 'fr' ? 'Miel de Thym' : 'Thyme Honey',
        price: 120,
        image: '/placeholder.svg',
        category: 'honey',
        discountPercentage: 0
      },
      navigationPath: '/honey'
    },
    {
      id: 2,
      image: "https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'زيت أركان نقي عضوي' : language === 'fr' ? 'Huile d\'Argan Pure Bio' : 'Pure Organic Argan Oil',
      subtitle: language === 'ar' ? 'من قلب المغرب، جودة استثنائية' : language === 'fr' ? 'Du cœur du Maroc, qualité exceptionnelle' : 'From the heart of Morocco, exceptional quality',
      description: language === 'ar' ? 'زيت أركان عضوي 100% مستخرج بالطرق التقليدية من تعاونيات نسائية' : language === 'fr' ? 'Huile d\'argan 100% bio extraite selon les méthodes traditionnelles par des coopératives féminines' : '100% organic argan oil extracted using traditional methods by women\'s cooperatives',
      cta: t('hero.cta'),
      ctaSecondary: t('hero.ctaSecondary'),
      bgGradient: 'from-olive/95 via-olive/80 to-olive/60',
      textColor: 'text-white',
      features: [t('hero.features.organic'), t('hero.features.traditional'), 'Anti-aging'],
      rating: 4.8,
      product: {
        id: 'argan_1',
        title: language === 'ar' ? 'زيت أركان للطهي' : language === 'fr' ? 'Huile d\'Argan Culinaire' : 'Culinary Argan Oil',
        price: 180,
        image: '/placeholder.svg',
        category: 'argan',
        discountPercentage: 0
      },
      navigationPath: '/argan'
    },
    {
      id: 3,
      image: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'عسل طبيعي صافي' : language === 'fr' ? 'Miel Naturel Pur' : 'Pure Natural Honey',
      subtitle: language === 'ar' ? 'حلاوة الطبيعة في كل قطرة' : language === 'fr' ? 'La douceur de la nature dans chaque goutte' : 'Nature\'s sweetness in every drop',
      description: language === 'ar' ? 'عسل طبيعي من جبال الأطلس، غني بالفيتامينات والمعادن' : language === 'fr' ? 'Miel naturel des montagnes de l\'Atlas, riche en vitamines et minéraux' : 'Natural honey from the Atlas Mountains, rich in vitamins and minerals',
      cta: t('hero.cta'),
      ctaSecondary: t('hero.ctaSecondary'),
      bgGradient: 'from-golden/95 via-golden/80 to-golden/60',
      textColor: 'text-white',
      features: [t('hero.features.natural'), 'Rich in antioxidants', 'Therapeutic'],
      rating: 4.7,
      product: {
        id: 'honey_3',
        title: language === 'ar' ? 'عسل الزهور البرية' : language === 'fr' ? 'Miel de Fleurs Sauvages' : 'Wildflower Honey',
        price: 110,
        image: '/placeholder.svg',
        category: 'honey',
        discountPercentage: 0
      },
      navigationPath: '/honey'
    },
    {
      id: 4,
      image: "https://images.unsplash.com/photo-1599894630746-8b8e0f2d4d00?auto=format&fit=crop&w=1920&q=80",
      title: language === 'ar' ? 'أملو تقليدي أصيل' : language === 'fr' ? 'Amlou Traditionnel Authentique' : 'Authentic Traditional Amlou',
      subtitle: language === 'ar' ? 'وصفة أصيلة بنكهة المغرب' : language === 'fr' ? 'Recette authentique au goût du Maroc' : 'Authentic recipe with Moroccan flavor',
      description: language === 'ar' ? 'أملو تقليدي مصنوع من اللوز وزيت الأركان والعسل الطبيعي' : language === 'fr' ? 'Amlou traditionnel fait d\'amandes, d\'huile d\'argan et de miel naturel' : 'Traditional amlou made from almonds, argan oil and natural honey',
      cta: t('hero.cta'),
      ctaSecondary: t('hero.ctaSecondary'),
      bgGradient: 'from-saffron/95 via-saffron/80 to-saffron/60',
      textColor: 'text-white',
      features: [t('hero.features.traditional'), t('hero.features.handmade'), 'Nutritious'],
      rating: 4.6,
      product: {
        id: 'amlou_1',
        title: language === 'ar' ? 'أملو تقليدي باللوز' : language === 'fr' ? 'Amlou Traditionnel aux Amandes' : 'Traditional Almond Amlou',
        price: 135,
        image: '/placeholder.svg',
        category: 'amlou',
        discountPercentage: 0
      },
      navigationPath: '/amlou'
    }
  ];

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  }, [slides.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  }, [slides.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  // Handle add to cart functionality
  const handleAddToCart = useCallback((slideData: Slide) => {
    if (isAddingToCart) return;

    setIsAddingToCart(true);

    try {
      const finalPrice = slideData.product.discountPercentage
        ? slideData.product.price * (1 - slideData.product.discountPercentage / 100)
        : slideData.product.price;

      addToCart({
        id: slideData.product.id,
        title: slideData.product.title,
        price: finalPrice,
        originalPrice: slideData.product.discountPercentage ? slideData.product.price : undefined,
        image: slideData.product.image,
        category: slideData.product.category,
        discountPercentage: slideData.product.discountPercentage
      });

      // Show success toast
      setTimeout(() => {
        setIsAddingToCart(false);
        showAddToCartToast(slideData.product.title, slideData.product.image);
        toast.success(
          language === 'ar'
            ? 'تمت إضافة المنتج إلى السلة'
            : language === 'fr'
              ? 'Produit ajouté au panier'
              : 'Product added to cart'
        );
      }, 600);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setIsAddingToCart(false);
      toast.error(
        language === 'ar'
          ? 'حدث خطأ أثناء إضافة المنتج'
          : language === 'fr'
            ? 'Erreur lors de l\'ajout du produit'
            : 'Error adding product to cart'
      );
    }
  }, [isAddingToCart, addToCart, showAddToCartToast, language]);

  // Handle navigation functionality
  const handleDiscoverMore = useCallback((slideData: Slide) => {
    try {
      navigate(slideData.navigationPath);
    } catch (error) {
      console.error('Navigation error:', error);
      toast.error(
        language === 'ar'
          ? 'حدث خطأ في التنقل'
          : language === 'fr'
            ? 'Erreur de navigation'
            : 'Navigation error'
      );
    }
  }, [navigate, language]);

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && !isHovered) {
      const interval = setInterval(nextSlide, 5000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, isHovered, nextSlide]);

  const currentSlideData = slides[currentSlide];

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0,
    }),
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div 
      className="relative h-screen w-full overflow-hidden bg-gradient-to-br from-gray-900 to-gray-800"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Images */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          className="absolute inset-0"
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{ duration: 0.8, ease: "easeInOut" }}
        >
          <div className="relative h-full w-full">
            <img
              src={currentSlideData.image}
              alt={currentSlideData.title}
              className="h-full w-full object-cover"
            />
            <div className={`absolute inset-0 bg-gradient-to-r ${currentSlideData.bgGradient}`} />
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="relative z-10 flex h-full items-center">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <motion.div
              key={`content-${currentSlide}`}
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              className={`space-y-6 ${currentSlideData.textColor}`}
            >
              {/* Promo Badge */}
              {currentSlideData.promo && (
                <motion.div variants={itemVariants}>
                  <Badge variant="secondary" className="bg-white/20 text-white border-white/30 backdrop-blur-sm">
                    {currentSlideData.promo}
                  </Badge>
                </motion.div>
              )}

              {/* Title */}
              <motion.h1 
                variants={itemVariants}
                className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
              >
                {currentSlideData.title}
              </motion.h1>

              {/* Subtitle */}
              <motion.p 
                variants={itemVariants}
                className="text-xl md:text-2xl font-medium opacity-90"
              >
                {currentSlideData.subtitle}
              </motion.p>

              {/* Description */}
              <motion.p 
                variants={itemVariants}
                className="text-lg opacity-80 max-w-2xl"
              >
                {currentSlideData.description}
              </motion.p>

              {/* Features */}
              <motion.div 
                variants={itemVariants}
                className={`flex flex-wrap gap-3 ${isRTL ? 'justify-end' : 'justify-start'}`}
              >
                {currentSlideData.features.map((feature, index) => (
                  <Badge key={index} variant="outline" className="bg-white/10 text-white border-white/30 backdrop-blur-sm">
                    {feature}
                  </Badge>
                ))}
              </motion.div>

              {/* Rating */}
              {currentSlideData.rating && (
                <motion.div 
                  variants={itemVariants}
                  className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}
                >
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${
                          i < Math.floor(currentSlideData.rating!)
                            ? 'text-yellow-400 fill-current'
                            : 'text-white/50'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-white/90 font-medium">{currentSlideData.rating}</span>
                </motion.div>
              )}

              {/* CTA Buttons */}
              <motion.div
                variants={itemVariants}
                className={`flex flex-col sm:flex-row gap-4 pt-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}
              >
                <Button
                  size="lg"
                  className="bg-white text-gray-900 hover:bg-white/90 font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                  onClick={() => handleAddToCart(currentSlideData)}
                  disabled={isAddingToCart}
                >
                  <ShoppingCart className={`mr-2 h-5 w-5 ${isAddingToCart ? 'animate-pulse' : ''}`} />
                  {isAddingToCart
                    ? (language === 'ar' ? 'جاري الإضافة...' : language === 'fr' ? 'Ajout...' : 'Adding...')
                    : currentSlideData.cta
                  }
                </Button>
                {currentSlideData.ctaSecondary && (
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white/70 text-white bg-transparent hover:bg-white/20 hover:border-white backdrop-blur-sm transition-all duration-300"
                    onClick={() => handleDiscoverMore(currentSlideData)}
                  >
                    {currentSlideData.ctaSecondary}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                )}
              </motion.div>
            </motion.div>

            {/* Right side - Additional visual elements could go here */}
            <div className="hidden lg:block">
              {/* This space can be used for additional graphics, product images, or stats */}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* Slide Indicators */}
          <div className="flex gap-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                }`}
              />
            ))}
          </div>

          {/* Play/Pause Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={togglePlayPause}
            className="text-white hover:bg-white/20"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Navigation Arrows */}
      <Button
        variant="ghost"
        size="icon"
        onClick={prevSlide}
        className={`absolute top-1/2 transform -translate-y-1/2 z-20 text-white hover:bg-white/20 ${
          isRTL ? 'right-4' : 'left-4'
        }`}
      >
        {isRTL ? <ChevronRight className="h-6 w-6" /> : <ChevronLeft className="h-6 w-6" />}
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={nextSlide}
        className={`absolute top-1/2 transform -translate-y-1/2 z-20 text-white hover:bg-white/20 ${
          isRTL ? 'left-4' : 'right-4'
        }`}
      >
        {isRTL ? <ChevronLeft className="h-6 w-6" /> : <ChevronRight className="h-6 w-6" />}
      </Button>
    </div>
  );
};

export default ModernHeroCarousel;
