
import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Leaf, Heart } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useFavorites } from '@/contexts/FavoritesContext';
import { toast } from 'sonner';

interface ProductImageSectionProps {
  id: string;
  image: string;
  title: string;
  isOrganic?: boolean;
  discountPercentage?: number;
  itemVariants: any;
}

const ProductImageSection: React.FC<ProductImageSectionProps> = ({
  id,
  image,
  title,
  isOrganic,
  discountPercentage,
  itemVariants
}) => {
  const { language } = useLanguage();
  const { addToFavorites, removeFromFavorites, isFavorite } = useFavorites();
  
  const productIsFavorite = isFavorite(id);

  const handleToggleFavorite = () => {
    if (productIsFavorite) {
      removeFromFavorites(id);
      toast.success(
        language === 'ar' 
          ? 'تمت إزالة المنتج من المفضلة' 
          : language === 'fr' 
            ? 'Produit retiré des favoris' 
            : 'Removed from favorites'
      );
    } else {
      addToFavorites(id);
      toast.success(
        language === 'ar' 
          ? 'تمت إضافة المنتج إلى المفضلة' 
          : language === 'fr' 
            ? 'Produit ajouté aux favoris' 
            : 'Added to favorites'
      );
    }
  };

  return (
    <motion.div 
      className="relative overflow-hidden rounded-lg border border-gray-200"
      variants={itemVariants}
    >
      <motion.img 
        src={image || "/placeholder.svg"} 
        alt={title}
        className="w-full h-auto object-cover aspect-square"
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300 }}
      />
      
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        {isOrganic && (
          <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
            <Leaf className="mr-1 h-3 w-3" />
            {language === 'ar' ? 'عضوي' : language === 'fr' ? 'Bio' : 'Organic'}
          </Badge>
        )}
        
        {discountPercentage && discountPercentage > 0 && (
          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
            {language === 'ar' ? 'عروض' : language === 'fr' ? 'Promo' : 'Sale'} {discountPercentage}%
          </Badge>
        )}
      </div>

      <Button 
        size="icon" 
        variant="outline" 
        className={`absolute top-4 left-4 rounded-full bg-white/80 backdrop-blur-sm ${
          productIsFavorite ? 'text-red-500 bg-red-50' : ''
        }`}
        onClick={handleToggleFavorite}
      >
        <Heart className={`h-4 w-4 ${productIsFavorite ? 'fill-current' : ''}`} />
      </Button>
    </motion.div>
  );
};

export default ProductImageSection;
