
import React from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const FeaturedProducts: React.FC = () => {
  const { t, language, isRTL } = useLanguage();

  // Sample products
  const products = [
    {
      id: 1,
      name: language === 'ar' ? 'زيت أركان عضوي' : language === 'fr' ? 'Huile d\'Argan Bio' : 'Organic Argan Oil',
      image: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=600&q=80',
      price: language === 'ar' ? '١٥٠ درهم' : '150 MAD',
      certified: true,
    },
    {
      id: 2,
      name: language === 'ar' ? 'أملو تقليدي' : language === 'fr' ? 'Amlou Traditionnel' : 'Traditional Amlou',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?auto=format&fit=crop&w=600&q=80',
      price: language === 'ar' ? '١٢٠ درهم' : '120 MAD',
      certified: true,
    },
    {
      id: 3,
      name: language === 'ar' ? 'عسل الأزهار البرية' : language === 'fr' ? 'Miel de Fleurs Sauvages' : 'Wildflower Honey',
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=600&q=80',
      price: language === 'ar' ? '٨٠ درهم' : '80 MAD',
      certified: true,
    },
    {
      id: 4,
      name: language === 'ar' ? 'صابون أركان' : language === 'fr' ? 'Savon à l\'Argan' : 'Argan Soap',
      image: 'https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=600&q=80',
      price: language === 'ar' ? '٥٠ درهم' : '50 MAD',
      certified: true,
    }
  ];

  return (
    <section className="py-16">
      <div className="container-custom">
        <h2 
          className={`text-3xl font-bold mb-12 text-center
          ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
        >
          {language === 'ar' ? 'منتجات مميزة' : language === 'fr' ? 'Produits Vedettes' : 'Featured Products'}
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <Card key={product.id} className="overflow-hidden group hover:shadow-lg transition-all">
              <div className="relative">
                <div className="aspect-square overflow-hidden">
                  <img 
                    src={product.image} 
                    alt={product.name}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                </div>
                
                {product.certified && (
                  <Badge 
                    className={`absolute top-2 ${isRTL ? 'left-2' : 'right-2'} bg-olive text-white`}
                  >
                    <span className={language === 'ar' ? 'font-arabic' : 'font-body'}>
                      {t('product.certified')}
                    </span>
                  </Badge>
                )}
              </div>
              
              <CardContent className={`p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                <h3 
                  className={`text-lg font-bold mb-2
                  ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
                >
                  {product.name}
                </h3>
                <p 
                  className={`text-lg font-medium text-olive
                  ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                >
                  {product.price}
                </p>
              </CardContent>
              
              <CardFooter className="p-4 pt-0">
                <Button 
                  className={`w-full bg-sand text-white hover:bg-olive
                  ${language === 'ar' ? 'font-arabic' : 'font-body'}`}
                >
                  {t('product.addToCart')}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
