
import React from 'react';
import { useI18n } from '@/hooks/useI18n';
import { motion } from 'framer-motion';
import { Star, ShoppingCart, Heart, Eye, Badge as BadgeIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';

interface FeaturedProduct {
  id: number;
  name: string;
  category: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviewCount: number;
  image: string;
  badge?: string;
  description: string;
  features: string[];
  link: string;
}

const FeaturedProducts: React.FC = () => {
  const { t, language, isRTL } = useI18n();

  const featuredProducts: FeaturedProduct[] = [
    {
      id: 1,
      name: language === 'ar' ? 'زيت الأركان العضوي النقي' : language === 'fr' ? 'Huile d\'Argan Bio Pure' : 'Pure Organic Argan Oil',
      category: language === 'ar' ? 'زيت الأركان' : language === 'fr' ? 'Huile d\'Argan' : 'Argan Oil',
      price: 299,
      originalPrice: 399,
      rating: 4.9,
      reviewCount: 127,
      image: 'https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=400&q=80',
      badge: language === 'ar' ? 'الأكثر مبيعاً' : language === 'fr' ? 'Bestseller' : 'Bestseller',
      description: language === 'ar' ? 'زيت أركان عضوي 100% مستخرج بالطرق التقليدية' : language === 'fr' ? 'Huile d\'argan 100% bio extraite selon les méthodes traditionnelles' : '100% organic argan oil extracted using traditional methods',
      features: [
        language === 'ar' ? 'عضوي معتمد' : language === 'fr' ? 'Bio certifié' : 'Certified Organic',
        language === 'ar' ? 'مضاد للشيخوخة' : language === 'fr' ? 'Anti-âge' : 'Anti-aging',
        language === 'ar' ? 'للبشرة والشعر' : language === 'fr' ? 'Peau et cheveux' : 'Skin & Hair'
      ],
      link: '/argan-oil'
    },
    {
      id: 2,
      name: language === 'ar' ? 'عسل الأطلس الطبيعي' : language === 'fr' ? 'Miel Naturel de l\'Atlas' : 'Natural Atlas Honey',
      category: language === 'ar' ? 'العسل' : language === 'fr' ? 'Miel' : 'Honey',
      price: 189,
      rating: 4.8,
      reviewCount: 89,
      image: 'https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07?auto=format&fit=crop&w=400&q=80',
      badge: language === 'ar' ? 'جديد' : language === 'fr' ? 'Nouveau' : 'New',
      description: language === 'ar' ? 'عسل طبيعي من جبال الأطلس، غني بالفيتامينات' : language === 'fr' ? 'Miel naturel des montagnes de l\'Atlas, riche en vitamines' : 'Natural honey from Atlas Mountains, rich in vitamins',
      features: [
        language === 'ar' ? 'طبيعي 100%' : language === 'fr' ? '100% naturel' : '100% Natural',
        language === 'ar' ? 'غني بمضادات الأكسدة' : language === 'fr' ? 'Riche en antioxydants' : 'Rich in Antioxidants',
        language === 'ar' ? 'خصائص علاجية' : language === 'fr' ? 'Propriétés thérapeutiques' : 'Therapeutic Properties'
      ],
      link: '/honey'
    },
    {
      id: 3,
      name: language === 'ar' ? 'أملو تقليدي بالعسل' : language === 'fr' ? 'Amlou Traditionnel au Miel' : 'Traditional Honey Amlou',
      category: language === 'ar' ? 'الأملو' : language === 'fr' ? 'Amlou' : 'Amlou',
      price: 149,
      originalPrice: 179,
      rating: 4.7,
      reviewCount: 156,
      image: 'https://images.unsplash.com/photo-1599894630746-8b8e0f2d4d00?auto=format&fit=crop&w=400&q=80',
      badge: language === 'ar' ? 'عرض خاص' : language === 'fr' ? 'Offre spéciale' : 'Special Offer',
      description: language === 'ar' ? 'أملو تقليدي مصنوع من اللوز وزيت الأركان والعسل' : language === 'fr' ? 'Amlou traditionnel fait d\'amandes, d\'huile d\'argan et de miel' : 'Traditional amlou made from almonds, argan oil and honey',
      features: [
        language === 'ar' ? 'وصفة تقليدية' : language === 'fr' ? 'Recette traditionnelle' : 'Traditional Recipe',
        language === 'ar' ? 'مصنوع يدوياً' : language === 'fr' ? 'Fait à la main' : 'Handmade',
        language === 'ar' ? 'مغذي وصحي' : language === 'fr' ? 'Nutritif et sain' : 'Nutritious & Healthy'
      ],
      link: '/amlou'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('featured.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('featured.subtitle')}
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {featuredProducts.map((product) => (
            <motion.div
              key={product.id}
              variants={itemVariants}
              whileHover={{ y: -10 }}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-gray-100">
                {/* Product Image */}
                <div className="relative overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  />

                  {/* Badge */}
                  {product.badge && (
                    <Badge className="absolute top-4 left-4 bg-golden text-white">
                      <BadgeIcon className="w-3 h-3 mr-1" />
                      {product.badge}
                    </Badge>
                  )}

                  {/* Quick Actions */}
                  <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button size="icon" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="secondary" className="bg-white/90 hover:bg-white">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Discount Badge */}
                  {product.originalPrice && (
                    <div className="absolute bottom-4 left-4">
                      <Badge variant="destructive">
                        -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className="p-6">
                  <div className="mb-2">
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>

                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-golden transition-colors">
                    {product.name}
                  </h3>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {product.description}
                  </p>

                  {/* Features */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {product.features.map((feature, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>

                  {/* Rating */}
                  <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(product.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      {product.rating} ({product.reviewCount})
                    </span>
                  </div>

                  {/* Price */}
                  <div className={`flex items-center gap-2 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <span className="text-2xl font-bold text-gray-900">
                      {product.price} {t('common.currency')}
                    </span>
                    {product.originalPrice && (
                      <span className="text-lg text-gray-500 line-through">
                        {product.originalPrice} {t('common.currency')}
                      </span>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button asChild className="flex-1">
                      <Link to={product.link}>
                        <ShoppingCart className="w-4 h-4 mr-2" />
                        {t('products.addToCart')}
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View All Products CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center mt-16"
        >
          <Button asChild size="lg" variant="outline" className="border-golden text-golden hover:bg-golden hover:text-white">
            <Link to="/products">
              {t('featured.viewAll')}
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  );
};

export default FeaturedProducts;
