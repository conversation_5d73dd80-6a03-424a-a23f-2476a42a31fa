
import React, { useState } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { useCart } from '@/contexts/CartContext';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Menu, ShoppingCart, MapPin } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import MainNavigationMenu from './NavigationMenu';
import CartDrawer from './CartDrawer';
import SearchBar from './SearchBar';

const Header: React.FC = () => {
  const { language, setLanguage, t, isRTL } = useI18n();
  const { state: cartState } = useCart();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  const menuItems = [
    { key: 'home', href: '/' },
    { key: 'argan', href: '/argan' },
    { key: 'honey', href: '/honey' },
    { key: 'amlou', href: '/amlou' },
    { key: 'deals', href: '/deals' },
    { key: 'about', href: '/about' },
    { key: 'contact', href: '/contact' },
    { key: 'privacy', href: '/privacy' },
  ];

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleCart = () => setIsCartOpen(!isCartOpen);

  const languageOptions = [
    { code: 'ar', name: 'العربية', flag: '🇲🇦' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'en', name: 'English', flag: '🇬🇧' },
  ];

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="sticky top-0 z-50 bg-white shadow-md"
    >
      <div className="w-full px-3 sm:px-4 md:px-6 lg:px-8 py-3 sm:py-4">
        <div className="flex items-center justify-between gap-2 sm:gap-4">
          {/* Logo */}
          <div className="flex-shrink-0">
            <motion.a
              href="/"
              className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-olive text-white flex items-center justify-center font-title text-sm sm:text-xl">IB</div>
              <span className={`text-lg sm:text-xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive hidden xs:block`}>
                {language === 'ar' ? 'إيميل بيو' : 'Imil Bio'}
              </span>
            </motion.a>
          </div>

          {/* Desktop Navigation */}
          <MainNavigationMenu />

          <div className={`flex items-center space-x-2 sm:space-x-3 md:space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            {/* Search Bar */}
            <div className="hidden sm:block">
              <SearchBar />
            </div>

            {/* Location Widget */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                className="hidden lg:flex items-center space-x-2 text-gray-600 hover:text-olive min-h-[44px] px-3"
                onClick={() => window.open('https://maps.google.com/?q=Imlil,+Morocco', '_blank')}
              >
                <MapPin className="h-4 w-4" />
                <span className={`text-sm ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                  {language === 'ar' ? 'إيميل، المغرب' : language === 'fr' ? 'Imlil, Maroc' : 'Imlil, Morocco'}
                </span>
              </Button>
            </motion.div>

            {/* Cart Icon */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="ghost" size="icon" className="relative min-h-[44px] min-w-[44px]" onClick={toggleCart}>
                <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
                <AnimatePresence>
                  {cartState.itemCount > 0 && (
                    <motion.span
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      className="absolute -top-1 -right-1 bg-olive text-white text-xs min-w-[18px] sm:min-w-[20px] h-4 sm:h-5 rounded-full flex items-center justify-center px-1 font-medium shadow-lg"
                    >
                      {cartState.itemCount > 99 ? '99+' : cartState.itemCount}
                    </motion.span>
                  )}
                </AnimatePresence>
              </Button>
            </motion.div>
            
            {/* Cart Drawer */}
            <CartDrawer isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
            
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button variant="outline" size="sm" className="px-2 sm:px-3 min-h-[44px]">
                    <span className="mr-1">
                      {languageOptions.find(option => option.code === language)?.flag}
                    </span>
                    <span className={`text-xs sm:text-sm ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                      {languageOptions.find(option => option.code === language)?.code.toUpperCase()}
                    </span>
                  </Button>
                </motion.div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                {languageOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.code}
                    onClick={() => setLanguage(option.code as 'ar' | 'fr' | 'en')}
                    className={`cursor-pointer ${option.code === 'ar' ? 'font-arabic' : 'font-body'}`}
                  >
                    <span className="mr-2">{option.flag}</span>
                    <span>{option.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Menu Button */}
            <motion.button
              className="md:hidden flex items-center justify-center min-h-[44px] min-w-[44px] rounded-md hover:bg-gray-100 transition-colors"
              onClick={toggleMenu}
              aria-label="Toggle menu"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={{ rotate: isMenuOpen ? 90 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <Menu className="h-6 w-6" />
              </motion.div>
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.nav
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="md:hidden py-4 border-t mt-2 overflow-hidden"
            >
              <motion.ul
                className={`flex flex-col space-y-3 ${isRTL ? 'text-right' : 'text-left'}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                {menuItems.map((item, index) => (
                  <motion.li
                    key={item.key}
                    initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <a
                      href={item.href}
                      className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive block transition duration-200 py-2 px-2 rounded-md hover:bg-gray-50 min-h-[44px] flex items-center`}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {t(item.key)}
                    </a>
                  </motion.li>
                ))}
                {/* Mobile Search */}
                <motion.li
                  initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: menuItems.length * 0.1 }}
                  className="sm:hidden pt-2 border-t"
                >
                  <SearchBar />
                </motion.li>
              </motion.ul>
            </motion.nav>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

export default Header;
