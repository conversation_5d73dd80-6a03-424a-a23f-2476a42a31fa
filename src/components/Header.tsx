
import React, { useState } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Menu, ShoppingCart, MapPin } from 'lucide-react';
import MainNavigationMenu from './NavigationMenu';
import CartDrawer from './CartDrawer';
import SearchBar from './SearchBar';

const Header: React.FC = () => {
  const { language, setLanguage, t, isRTL } = useI18n();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [cartItemCount, setCartItemCount] = useState(0);

  const menuItems = [
    { key: 'home', href: '/' },
    { key: 'argan', href: '/argan' },
    { key: 'honey', href: '/honey' },
    { key: 'amlou', href: '/amlou' },
    { key: 'about', href: '/about' },
    { key: 'contact', href: '/contact' },
    { key: 'privacy', href: '/privacy' },
  ];

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const toggleCart = () => setIsCartOpen(!isCartOpen);

  const languageOptions = [
    { code: 'ar', name: 'العربية', flag: '🇲🇦' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'en', name: 'English', flag: '🇬🇧' },
  ];

  return (
    <header className="sticky top-0 z-50 bg-white shadow-md">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <a href="/" className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
              <div className="w-10 h-10 rounded-full bg-olive text-white flex items-center justify-center font-title text-xl">IB</div>
              <span className={`text-xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'} text-olive`}>
                {language === 'ar' ? 'إيميل بيو' : 'Imil Bio'}
              </span>
            </a>
          </div>

          {/* Desktop Navigation */}
          <MainNavigationMenu />

          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            {/* Search Bar */}
            <SearchBar />

            {/* Location Widget */}
            <Button
              variant="ghost"
              size="sm"
              className="hidden lg:flex items-center space-x-2 text-gray-600 hover:text-olive"
              onClick={() => window.open('https://maps.google.com/?q=Imlil,+Morocco', '_blank')}
            >
              <MapPin className="h-4 w-4" />
              <span className={`text-sm ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                {language === 'ar' ? 'إيميل، المغرب' : language === 'fr' ? 'Imlil, Maroc' : 'Imlil, Morocco'}
              </span>
            </Button>

            {/* Cart Icon */}
            <Button variant="ghost" size="icon" className="relative" onClick={toggleCart}>
              <ShoppingCart className="h-6 w-6" />
              {cartItemCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center">
                  {cartItemCount}
                </span>
              )}
            </Button>
            
            {/* Cart Drawer */}
            <CartDrawer isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />
            
            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="px-2">
                  <span className="mr-1">
                    {languageOptions.find(option => option.code === language)?.flag}
                  </span>
                  <span className={`${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {languageOptions.find(option => option.code === language)?.code.toUpperCase()}
                  </span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                {languageOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.code}
                    onClick={() => setLanguage(option.code as 'ar' | 'fr' | 'en')}
                    className={`cursor-pointer ${option.code === 'ar' ? 'font-arabic' : 'font-body'}`}
                  >
                    <span className="mr-2">{option.flag}</span>
                    <span>{option.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Mobile Menu Button */}
            <button 
              className="md:hidden flex items-center" 
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <nav className="md:hidden py-4 border-t mt-2 animate-fade-in">
            <ul className={`flex flex-col space-y-4 ${isRTL ? 'text-right' : 'text-left'}`}>
              {menuItems.map((item) => (
                <li key={item.key}>
                  <a 
                    href={item.href} 
                    className={`${language === 'ar' ? 'font-arabic' : 'font-body'} text-gray-800 hover:text-olive block transition duration-200`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t(item.key)}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
