
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ProductImageSection from '@/components/ProductImageSection';
import ProductInfoSection from '@/components/ProductInfoSection';
import ProductOptions from '@/components/ProductOptions';
import ProductTabs from '@/components/ProductTabs';
import AddToCartButton from '@/components/AddToCartButton';
import UsageTips from '@/components/UsageTips';

interface ProductDetailProps {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isOrganic?: boolean;
  discountPercentage?: number;
  benefits?: string[];
  description?: string;
  extractionMethod?: string;
  viscosity?: string;
  producer?: {
    name: string;
    location: string;
    womenMembers: number;
  };
}

const ProductDetail: React.FC<ProductDetailProps> = ({
  id,
  title,
  price,
  originalPrice,
  image,
  category,
  isOrganic,
  discountPercentage,
  benefits = [],
  description,
  extractionMethod,
  viscosity,
  producer
}) => {
  const [selectedQuantity, setSelectedQuantity] = useState(1);
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, string>>({ size: '1kg' });
  const [totalPrice, setTotalPrice] = useState(price);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: { 
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };
  
  return (
    <motion.div 
      className="container-custom py-8"
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Image */}
        <ProductImageSection
          id={id}
          image={image}
          title={title}
          isOrganic={isOrganic}
          discountPercentage={discountPercentage}
          itemVariants={itemVariants}
        />

        {/* Product Info */}
        <div className="space-y-6">
          <ProductInfoSection
            title={title}
            price={price}
            itemVariants={itemVariants}
          />

          {/* Product Options */}
          <ProductOptions
            category={category}
            basePrice={price}
            onQuantityChange={setSelectedQuantity}
            onAttributesChange={setSelectedAttributes}
            onTotalPriceChange={setTotalPrice}
          />

          {/* Product Tabs */}
          <ProductTabs
            description={description}
            benefits={benefits}
            itemVariants={itemVariants}
          />
          
          {/* Add to Cart Button */}
          <AddToCartButton
            id={id}
            title={title}
            price={price}
            originalPrice={originalPrice}
            image={image}
            category={category}
            discountPercentage={discountPercentage}
            totalPrice={totalPrice}
            selectedQuantity={selectedQuantity}
            selectedAttributes={selectedAttributes}
            itemVariants={itemVariants}
          />

          {/* Usage Tips */}
          <UsageTips itemVariants={itemVariants} />
        </div>
      </div>
    </motion.div>
  );
};

export default ProductDetail;
