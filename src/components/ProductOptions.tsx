
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Minus, Plus } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { motion } from 'framer-motion';

interface ProductOptionsProps {
  category: string;
  basePrice: number;
  onQuantityChange: (quantity: number) => void;
  onAttributesChange: (attributes: Record<string, string>) => void;
  onTotalPriceChange: (totalPrice: number) => void;
  initialQuantity?: number;
}

const ProductOptions: React.FC<ProductOptionsProps> = ({
  category,
  basePrice,
  onQuantityChange,
  onAttributesChange,
  onTotalPriceChange,
  initialQuantity = 1
}) => {
  const { language } = useLanguage();
  const [quantity, setQuantity] = useState(initialQuantity);
  const [selectedSize, setSelectedSize] = useState<string>('1kg');

  // Prix basé sur la taille (prix de base pour 1kg)
  const getSizeMultiplier = (size: string) => {
    switch (size) {
      case '250g':
        return 0.25;
      case '500g':
        return 0.5;
      case '1kg':
        return 1;
      case '2kg':
        return 2;
      default:
        return 1;
    }
  };

  // Calculer le prix total
  const calculateTotalPrice = () => {
    const sizeMultiplier = getSizeMultiplier(selectedSize);
    return basePrice * sizeMultiplier * quantity;
  };

  // Mettre à jour le prix total quand la quantité ou la taille change
  useEffect(() => {
    const totalPrice = calculateTotalPrice();
    onTotalPriceChange(totalPrice);
  }, [quantity, selectedSize, basePrice]);

  const handleQuantityChange = (newQuantity: number) => {
    const validQuantity = Math.max(1, Math.min(99, newQuantity));
    setQuantity(validQuantity);
    onQuantityChange(validQuantity);
  };

  const handleSizeChange = (size: string) => {
    setSelectedSize(size);
    onAttributesChange({ size });
  };

  // Options de taille disponibles
  const sizeOptions = [
    { value: '250g', label: '250g' },
    { value: '500g', label: '500g' },
    { value: '1kg', label: '1kg' },
    { value: '2kg', label: '2kg' }
  ];

  return (
    <motion.div 
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      {/* Sélection de la taille */}
      <div className="space-y-3">
        <h4 className={`font-medium ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
          {language === 'ar' ? 'الحجم' : language === 'fr' ? 'Taille' : 'Size'}
        </h4>
        <div className="flex flex-wrap gap-2">
          {sizeOptions.map((option) => (
            <motion.div
              key={option.value}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Badge
                variant={selectedSize === option.value ? "default" : "outline"}
                className={`cursor-pointer transition-all ${
                  selectedSize === option.value 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-gray-100'
                }`}
                onClick={() => handleSizeChange(option.value)}
              >
                {option.label}
              </Badge>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Sélection de la quantité */}
      <div className="space-y-3">
        <h4 className={`font-medium ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
          {language === 'ar' ? 'الكمية' : language === 'fr' ? 'Quantité' : 'Quantity'}
        </h4>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="icon"
            onClick={() => handleQuantityChange(quantity - 1)}
            disabled={quantity <= 1}
          >
            <Minus className="h-4 w-4" />
          </Button>
          <motion.span 
            className="text-xl font-semibold min-w-[3rem] text-center"
            key={quantity}
            initial={{ scale: 1.2 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {quantity}
          </motion.span>
          <Button
            variant="outline"
            size="icon"
            onClick={() => handleQuantityChange(quantity + 1)}
            disabled={quantity >= 99}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Affichage du prix calculé */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex justify-between items-center">
          <span className={`font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            {language === 'ar' ? 'السعر الإجمالي:' : language === 'fr' ? 'Prix total:' : 'Total Price:'}
          </span>
          <motion.span 
            className="text-2xl font-bold text-primary"
            key={calculateTotalPrice()}
            initial={{ scale: 1.1 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            {calculateTotalPrice().toFixed(2)} {language === 'ar' ? 'درهم' : 'MAD'}
          </motion.span>
        </div>
        <div className="text-sm text-gray-600 mt-1">
          {selectedSize} × {quantity} {language === 'ar' ? 'قطعة' : language === 'fr' ? 'pièce(s)' : 'piece(s)'}
        </div>
      </div>
    </motion.div>
  );
};

export default ProductOptions;
