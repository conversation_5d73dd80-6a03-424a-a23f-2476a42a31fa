
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ShoppingCart, Check } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCart } from '@/contexts/CartContext';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface ProductCardProps {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isOrganic?: boolean;
  discountPercentage?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  title,
  price,
  originalPrice,
  image,
  category,
  isOrganic = false,
  discountPercentage = 0
}) => {
  const { language, isRTL } = useLanguage();
  const { addToCart } = useCart();
  const [isAdding, setIsAdding] = React.useState(false);
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsAdding(true);
    
    const finalPrice = discountPercentage ? price * (1 - discountPercentage / 100) : price;
    
    addToCart({
      id,
      title,
      price: finalPrice,
      originalPrice,
      image,
      category,
      discountPercentage
    });
    
    setTimeout(() => {
      setIsAdding(false);
      toast.success(
        language === 'ar' 
          ? 'تمت إضافة المنتج إلى السلة' 
          : language === 'fr' 
            ? 'Produit ajouté au panier' 
            : 'Product added to cart'
      );
    }, 600);
  };

  // Determine category-specific styling
  const getCategoryStyle = () => {
    switch (category) {
      case 'honey':
        return 'border-golden hover:border-golden/80';
      case 'argan':
        return 'border-olive hover:border-olive/80';
      case 'amlou':
        return 'border-sand hover:border-sand/80';
      default:
        return 'border-gray-200 hover:border-gray-300';
    }
  };

  return (
    <motion.div
      whileHover={{ y: -5 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <Link to={`/product/${id}`} className="block">
        <Card className={`overflow-hidden transition-all hover:shadow-md ${getCategoryStyle()} relative`}>
          {/* Badges */}
          <div className={`absolute top-2 ${isRTL ? 'left-2' : 'right-2'} z-10 flex flex-col gap-2`}>
            {isOrganic && (
              <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
                {language === 'ar' ? 'عضوي' : language === 'fr' ? 'Bio' : 'Organic'}
              </Badge>
            )}
            {discountPercentage > 0 && (
              <motion.div
                initial={{ scale: 1 }}
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1, repeat: Infinity, repeatDelay: 5 }}
              >
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                  {language === 'ar' ? 'عروض' : language === 'fr' ? 'Promo' : 'Sale'} {discountPercentage}%
                </Badge>
              </motion.div>
            )}
          </div>

          {/* Product Image */}
          <motion.div 
            className="relative pt-[100%] overflow-hidden"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <img 
              src={image || "/placeholder.svg"}
              alt={title}
              className="absolute top-0 left-0 w-full h-full object-cover transition-transform"
            />
          </motion.div>
          
          <CardHeader className={`${language === 'ar' ? 'text-right font-arabic' : 'text-left font-body'} pb-2`}>
            <h3 className="font-semibold line-clamp-2">{title}</h3>
          </CardHeader>
          
          <CardContent className={`${isRTL ? 'text-right' : 'text-left'} pb-2`}>
            <div className="flex flex-wrap items-center gap-2">
              <span className="font-semibold text-lg">
                {price.toFixed(2)} {language === 'ar' ? 'درهم' : language === 'fr' ? 'MAD' : 'MAD'}
              </span>
              {originalPrice && (
                <span className="text-gray-500 line-through text-sm">
                  {originalPrice.toFixed(2)} {language === 'ar' ? 'درهم' : language === 'fr' ? 'MAD' : 'MAD'}
                </span>
              )}
            </div>
          </CardContent>
          
          <CardFooter>
            <Button 
              onClick={handleAddToCart} 
              size="sm"
              className={`w-full ${category === 'honey' ? 'bg-golden hover:bg-golden/90' : category === 'argan' ? 'bg-olive hover:bg-olive/90' : ''}`}
              disabled={isAdding}
            >
              <AnimatePresence mode="wait">
                {isAdding ? (
                  <motion.div
                    key="adding"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="flex items-center"
                  >
                    <Check className="h-4 w-4 mr-2" />
                    {language === 'ar' ? 'تمت الإضافة' : language === 'fr' ? 'Ajouté' : 'Added'}
                  </motion.div>
                ) : (
                  <motion.div
                    key="add"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="flex items-center"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {language === 'ar' ? 'أضف إلى السلة' : language === 'fr' ? 'Ajouter au panier' : 'Add to Cart'}
                  </motion.div>
                )}
              </AnimatePresence>
            </Button>
          </CardFooter>
        </Card>
      </Link>
    </motion.div>
  );
};

export default ProductCard;
