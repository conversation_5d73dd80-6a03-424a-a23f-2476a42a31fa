
import React from 'react';
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ShoppingCart, Check } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { useCart } from '@/contexts/CartContext';
import { useCartToast } from '@/components/CartToast';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

interface ProductCardProps {
  id: string;
  title: string;
  price: number;
  originalPrice?: number;
  image: string;
  category: string;
  isOrganic?: boolean;
  discountPercentage?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  id,
  title,
  price,
  originalPrice,
  image,
  category,
  isOrganic = false,
  discountPercentage = 0
}) => {
  const { language, isRTL } = useLanguage();
  const { addToCart } = useCart();
  const { showAddToCartToast } = useCartToast();
  const [isAdding, setIsAdding] = React.useState(false);
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsAdding(true);
    
    const finalPrice = discountPercentage ? price * (1 - discountPercentage / 100) : price;
    
    addToCart({
      id,
      title,
      price: finalPrice,
      originalPrice,
      image,
      category,
      discountPercentage
    });
    
    setTimeout(() => {
      setIsAdding(false);
      showAddToCartToast(title, image);
      toast.success(
        language === 'ar'
          ? 'تمت إضافة المنتج إلى السلة'
          : language === 'fr'
            ? 'Produit ajouté au panier'
            : 'Product added to cart'
      );
    }, 600);
  };

  // Determine category-specific styling
  const getCategoryStyle = () => {
    switch (category) {
      case 'honey':
        return 'border-golden hover:border-golden/80';
      case 'argan':
        return 'border-olive hover:border-olive/80';
      case 'amlou':
        return 'border-sand hover:border-sand/80';
      default:
        return 'border-gray-200 hover:border-gray-300';
    }
  };

  return (
    <motion.div
      whileHover={{ y: -5, scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className="h-full"
    >
      <Link to={`/product/${id}`} className="block h-full">
        <Card className={`overflow-hidden transition-all duration-300 hover:shadow-xl ${getCategoryStyle()} relative h-full flex flex-col`}>
          {/* Badges */}
          <div className={`absolute top-2 sm:top-3 ${isRTL ? 'left-2 sm:left-3' : 'right-2 sm:right-3'} z-10 flex flex-col gap-1 sm:gap-2`}>
            {isOrganic && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200 text-xs sm:text-sm px-2 py-1">
                  {language === 'ar' ? 'عضوي' : language === 'fr' ? 'Bio' : 'Organic'}
                </Badge>
              </motion.div>
            )}
            {discountPercentage > 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1, rotate: [0, -2, 2, 0] }}
                transition={{
                  opacity: { delay: 0.3 },
                  scale: { delay: 0.3 },
                  rotate: { duration: 2, repeat: Infinity, repeatDelay: 3 }
                }}
              >
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200 text-xs sm:text-sm px-2 py-1 font-bold">
                  {language === 'ar' ? 'عروض' : language === 'fr' ? 'Promo' : 'Sale'} {discountPercentage}%
                </Badge>
              </motion.div>
            )}
          </div>

          {/* Product Image */}
          <motion.div
            className="relative pt-[100%] overflow-hidden bg-gray-50"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <motion.img
              src={image || "/placeholder.svg"}
              alt={title}
              className="absolute top-0 left-0 w-full h-full object-cover"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              whileHover={{ scale: 1.1 }}
            />
            {/* Overlay gradient for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300" />
          </motion.div>

          <div className="flex-1 flex flex-col">
            <CardHeader className={`${language === 'ar' ? 'text-right font-arabic' : 'text-left font-body'} pb-2 px-3 sm:px-4 pt-3 sm:pt-4`}>
              <motion.h3
                className="font-semibold line-clamp-2 text-sm sm:text-base leading-tight"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.1 }}
              >
                {title}
              </motion.h3>
            </CardHeader>

            <CardContent className={`${isRTL ? 'text-right' : 'text-left'} pb-2 px-3 sm:px-4 flex-1`}>
              <motion.div
                className="flex flex-wrap items-center gap-1 sm:gap-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <span className="font-bold text-base sm:text-lg text-gray-900">
                  {price.toFixed(2)} {language === 'ar' ? 'درهم' : language === 'fr' ? 'MAD' : 'MAD'}
                </span>
                {originalPrice && (
                  <span className="text-gray-500 line-through text-xs sm:text-sm">
                    {originalPrice.toFixed(2)} {language === 'ar' ? 'درهم' : language === 'fr' ? 'MAD' : 'MAD'}
                  </span>
                )}
              </motion.div>
            </CardContent>
          </div>

          <CardFooter className="px-3 sm:px-4 pb-3 sm:pb-4 pt-0">
            <motion.div
              className="w-full"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={handleAddToCart}
                size="sm"
                className={`w-full min-h-[44px] text-xs sm:text-sm font-medium transition-all duration-200 ${
                  category === 'honey'
                    ? 'bg-golden hover:bg-golden/90 shadow-golden/20'
                    : category === 'argan'
                    ? 'bg-olive hover:bg-olive/90 shadow-olive/20'
                    : category === 'amlou'
                    ? 'bg-sand hover:bg-sand/90 shadow-sand/20'
                    : 'bg-gray-900 hover:bg-gray-800'
                } shadow-lg hover:shadow-xl`}
                disabled={isAdding}
              >
                <AnimatePresence mode="wait">
                  {isAdding ? (
                    <motion.div
                      key="adding"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      className="flex items-center justify-center gap-2"
                    >
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Check className="h-3 w-3 sm:h-4 sm:w-4" />
                      </motion.div>
                      <span className="hidden sm:inline">
                        {language === 'ar' ? 'تمت الإضافة' : language === 'fr' ? 'Ajouté' : 'Added'}
                      </span>
                    </motion.div>
                  ) : (
                    <motion.div
                      key="add"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0, opacity: 0 }}
                      className="flex items-center justify-center gap-2"
                    >
                      <ShoppingCart className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="hidden sm:inline">
                        {language === 'ar' ? 'أضف إلى السلة' : language === 'fr' ? 'Ajouter' : 'Add to Cart'}
                      </span>
                      <span className="sm:hidden">
                        {language === 'ar' ? 'أضف' : language === 'fr' ? '+' : '+'}
                      </span>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Button>
            </motion.div>
          </CardFooter>
        </Card>
      </Link>
    </motion.div>
  );
};

export default ProductCard;
