import React from 'react';
import { useI18n } from '@/hooks/useI18n';
import { motion } from 'framer-motion';
import { Leaf, Heart, Shield, Award, Sparkles, Users } from 'lucide-react';

interface Benefit {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: string;
}

const ProductBenefits: React.FC = () => {
  const { t, isRTL } = useI18n();

  const benefits: Benefit[] = [
    {
      icon: <Leaf className="h-8 w-8" />,
      title: t('benefits.organic.title'),
      description: t('benefits.organic.description'),
      color: 'text-green-600'
    },
    {
      icon: <Heart className="h-8 w-8" />,
      title: t('benefits.health.title'),
      description: t('benefits.health.description'),
      color: 'text-red-500'
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: t('benefits.quality.title'),
      description: t('benefits.quality.description'),
      color: 'text-blue-600'
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: t('benefits.traditional.title'),
      description: t('benefits.traditional.description'),
      color: 'text-golden'
    },
    {
      icon: <Sparkles className="h-8 w-8" />,
      title: t('benefits.natural.title'),
      description: t('benefits.natural.description'),
      color: 'text-purple-600'
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: t('benefits.community.title'),
      description: t('benefits.community.description'),
      color: 'text-orange-600'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {t('benefits.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('benefits.subtitle')}
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ 
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className="group"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                <div className={`${benefit.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {benefit.icon}
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {benefit.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed">
                  {benefit.description}
                </p>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-sand to-golden rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              {t('benefits.cta.title')}
            </h3>
            <p className="text-lg mb-8 opacity-90">
              {t('benefits.cta.description')}
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-gray-900 px-8 py-4 rounded-full font-semibold text-lg hover:bg-gray-100 transition-colors duration-300"
            >
              {t('benefits.cta.button')}
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProductBenefits;
