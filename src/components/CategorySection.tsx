
import React from 'react';
import { motion } from 'framer-motion';
import { useI18n } from '@/hooks/useI18n';
import { Card, CardContent } from '@/components/ui/card';

const CategorySection: React.FC = () => {
  const { t, language, isRTL } = useI18n();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  const titleVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 25
      }
    }
  };

  const categories = [
    { 
      id: 'argan',
      name: t('categories.argan'),
      image: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-olive/10',
      borderColor: 'border-olive'
    },
    { 
      id: 'amlou',
      name: t('categories.amlou'),
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-sand/10',
      borderColor: 'border-sand'
    },
    { 
      id: 'honey',
      name: t('categories.honey'),
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-saffron/10',
      borderColor: 'border-saffron'
    },
    { 
      id: 'cosmetics',
      name: t('categories.cosmetics'),
      image: 'https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-dark-olive/10',
      borderColor: 'border-dark-olive'
    }
  ];

  return (
    <section className="py-16 moroccan-pattern">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <motion.h2
          className={`text-3xl font-bold mb-12 text-center
          ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
          variants={titleVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          {t('categories.title')}
        </motion.h2>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              variants={itemVariants}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { type: "spring", stiffness: 400, damping: 25 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <a href={`#${category.id}`} className="group block">
                <Card className={`overflow-hidden transition-all duration-300 hover:shadow-xl
                  border-2 ${category.borderColor} ${category.bgColor} relative`}>
                  <div className="aspect-square overflow-hidden relative">
                    <motion.img
                      src={category.image}
                      alt={category.name}
                      className="w-full h-full object-cover"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.4, ease: "easeOut" }}
                    />
                    {/* Overlay gradient */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <CardContent className="p-4 relative">
                    <motion.h3
                      className={`text-xl font-bold text-center
                      ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 + index * 0.1 }}
                    >
                      {category.name}
                    </motion.h3>
                  </CardContent>
                </Card>
              </a>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default CategorySection;
