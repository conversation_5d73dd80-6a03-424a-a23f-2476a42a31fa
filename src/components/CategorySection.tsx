
import React from 'react';
import { useI18n } from '@/hooks/useI18n';
import { Card, CardContent } from '@/components/ui/card';

const CategorySection: React.FC = () => {
  const { t, language, isRTL } = useI18n();
  
  const categories = [
    { 
      id: 'argan',
      name: t('categories.argan'),
      image: 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-olive/10',
      borderColor: 'border-olive'
    },
    { 
      id: 'amlou',
      name: t('categories.amlou'),
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-sand/10',
      borderColor: 'border-sand'
    },
    { 
      id: 'honey',
      name: t('categories.honey'),
      image: 'https://images.unsplash.com/photo-1472396961693-142e6e269027?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-saffron/10',
      borderColor: 'border-saffron'
    },
    { 
      id: 'cosmetics',
      name: t('categories.cosmetics'),
      image: 'https://images.unsplash.com/photo-1482881497185-d4a9ddbe4151?auto=format&fit=crop&w=600&q=80',
      bgColor: 'bg-dark-olive/10',
      borderColor: 'border-dark-olive'
    }
  ];

  return (
    <section className="py-16 moroccan-pattern">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <h2 
          className={`text-3xl font-bold mb-12 text-center
          ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
        >
          {t('categories.title')}
        </h2>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <a href={`#${category.id}`} key={category.id} className="group">
              <Card className={`overflow-hidden transition-all hover:shadow-lg 
                border-2 ${category.borderColor} ${category.bgColor}`}>
                <div className="aspect-square overflow-hidden">
                  <img 
                    src={category.image} 
                    alt={category.name}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 
                    className={`text-xl font-bold text-center
                    ${language === 'ar' ? 'font-arabic' : 'font-title'}`}
                  >
                    {category.name}
                  </h3>
                </CardContent>
              </Card>
            </a>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategorySection;
