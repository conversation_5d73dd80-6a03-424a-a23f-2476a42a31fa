import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, ShoppingCart, Eye } from 'lucide-react';
import { useReducedMotion } from '@/hooks/useReducedMotion';
import { useNavigate } from 'react-router-dom';

export interface CartToastData {
  id: string;
  type: 'add' | 'remove' | 'update';
  productName: string;
  productImage?: string;
  quantity?: number;
  duration?: number;
}

interface CartToastContextType {
  toasts: CartToastData[];
  showAddToCartToast: (productName: string, productImage?: string, quantity?: number) => void;
  showRemoveFromCartToast: (productName: string) => void;
  showUpdateCartToast: (productName: string, quantity: number) => void;
  removeToast: (id: string) => void;
}

const CartToastContext = createContext<CartToastContextType | null>(null);

export const useCartToast = () => {
  const context = useContext(CartToastContext);
  if (!context) {
    throw new Error('useCartToast must be used within a CartToastProvider');
  }
  return context;
};

export const CartToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<CartToastData[]>([]);

  const addToast = useCallback((toast: Omit<CartToastData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };
    
    setToasts(prev => [...prev, newToast]);

    // Auto remove after duration
    const duration = toast.duration || 4000;
    setTimeout(() => {
      removeToast(id);
    }, duration);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const showAddToCartToast = useCallback((productName: string, productImage?: string, quantity = 1) => {
    addToast({
      type: 'add',
      productName,
      productImage,
      quantity,
      duration: 4000
    });
  }, [addToast]);

  const showRemoveFromCartToast = useCallback((productName: string) => {
    addToast({
      type: 'remove',
      productName,
      duration: 3000
    });
  }, [addToast]);

  const showUpdateCartToast = useCallback((productName: string, quantity: number) => {
    addToast({
      type: 'update',
      productName,
      quantity,
      duration: 3000
    });
  }, [addToast]);

  return (
    <CartToastContext.Provider value={{ 
      toasts, 
      showAddToCartToast, 
      showRemoveFromCartToast, 
      showUpdateCartToast, 
      removeToast 
    }}>
      {children}
      <CartToastContainer />
    </CartToastContext.Provider>
  );
};

const CartToastContainer: React.FC = () => {
  const { toasts, removeToast } = useCartToast();
  const prefersReducedMotion = useReducedMotion();
  const navigate = useNavigate();

  const getToastContent = (toast: CartToastData) => {
    switch (toast.type) {
      case 'add':
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          title: 'Added to Cart!',
          description: `${toast.productName}${toast.quantity && toast.quantity > 1 ? ` (${toast.quantity})` : ''} added to your cart`,
          bgColor: 'bg-green-50 border-green-200',
          showViewCart: true
        };
      case 'remove':
        return {
          icon: <X className="h-5 w-5 text-red-500" />,
          title: 'Removed from Cart',
          description: `${toast.productName} removed from your cart`,
          bgColor: 'bg-red-50 border-red-200',
          showViewCart: false
        };
      case 'update':
        return {
          icon: <ShoppingCart className="h-5 w-5 text-olive" />,
          title: 'Cart Updated',
          description: `${toast.productName} quantity updated to ${toast.quantity}`,
          bgColor: 'bg-olive/5 border-olive/20',
          showViewCart: false
        };
      default:
        return {
          icon: <ShoppingCart className="h-5 w-5 text-olive" />,
          title: 'Cart Updated',
          description: toast.productName,
          bgColor: 'bg-olive/5 border-olive/20',
          showViewCart: false
        };
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      <AnimatePresence>
        {toasts.map((toast) => {
          const content = getToastContent(toast);
          
          return (
            <motion.div
              key={toast.id}
              initial={prefersReducedMotion ? { opacity: 0 } : { 
                opacity: 0, 
                x: 300, 
                scale: 0.9,
                rotateY: 90
              }}
              animate={{ 
                opacity: 1, 
                x: 0, 
                scale: 1,
                rotateY: 0
              }}
              exit={prefersReducedMotion ? { opacity: 0 } : { 
                opacity: 0, 
                x: 300, 
                scale: 0.9,
                rotateY: -90
              }}
              transition={prefersReducedMotion ? { duration: 0.1 } : {
                type: "spring",
                stiffness: 300,
                damping: 25,
                duration: 0.6
              }}
              className={`
                ${content.bgColor}
                border rounded-xl shadow-lg p-4 backdrop-blur-sm
                max-w-sm w-full relative overflow-hidden
              `}
            >
              {/* Background animation */}
              {!prefersReducedMotion && (
                <motion.div
                  initial={{ x: '-100%' }}
                  animate={{ x: '100%' }}
                  transition={{ 
                    duration: toast.duration ? toast.duration / 1000 : 4,
                    ease: "linear"
                  }}
                  className="absolute top-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-olive/30 to-transparent"
                />
              )}

              <div className="flex items-start gap-3">
                {/* Product Image */}
                {toast.productImage && (
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ 
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                      delay: 0.1
                    }}
                    className="flex-shrink-0"
                  >
                    <img 
                      src={toast.productImage} 
                      alt={toast.productName}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  </motion.div>
                )}
                
                {/* Icon (if no image) */}
                {!toast.productImage && (
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ 
                      type: "spring",
                      stiffness: 300,
                      damping: 20,
                      delay: 0.1
                    }}
                    className="flex-shrink-0"
                  >
                    {content.icon}
                  </motion.div>
                )}
                
                <div className="flex-1 min-w-0">
                  <motion.h4 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-sm font-semibold text-gray-900 mb-1"
                  >
                    {content.title}
                  </motion.h4>
                  <motion.p 
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-sm text-gray-600"
                  >
                    {content.description}
                  </motion.p>
                  
                  {/* View Cart Button */}
                  {content.showViewCart && (
                    <motion.button
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      whileHover={prefersReducedMotion ? {} : { scale: 1.05 }}
                      whileTap={prefersReducedMotion ? {} : { scale: 0.95 }}
                      onClick={() => {
                        navigate('/cart');
                        removeToast(toast.id);
                      }}
                      className="mt-2 flex items-center gap-1 text-sm font-medium text-olive hover:text-olive/80 transition-colors"
                    >
                      <Eye className="h-3 w-3" />
                      View Cart
                    </motion.button>
                  )}
                </div>
                
                {/* Close Button */}
                <motion.button
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                  whileHover={prefersReducedMotion ? {} : { 
                    scale: 1.1,
                    rotate: 90
                  }}
                  whileTap={prefersReducedMotion ? {} : { scale: 0.9 }}
                  onClick={() => removeToast(toast.id)}
                  className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                </motion.button>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
};
