import React, { useState } from 'react';
import { useI18n } from '@/hooks/useI18n';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  rating: number;
  comment: string;
  product: string;
  avatar: string;
  verified: boolean;
}

const CustomerTestimonials: React.FC = () => {
  const { t, language, isRTL } = useI18n();
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials: Testimonial[] = [
    {
      id: 1,
      name: language === 'ar' ? 'فاطمة الزهراء' : language === 'fr' ? 'Fatima Zahra' : 'Fatima Zahra',
      location: language === 'ar' ? 'الرباط، المغرب' : language === 'fr' ? 'Rabat, Maroc' : 'Rabat, Morocco',
      rating: 5,
      comment: language === 'ar' 
        ? 'زيت الأركان من إمليل بيو رائع جداً! استخدمه لبشرتي وشعري والنتائج مذهلة. جودة عالية وسعر مناسب.'
        : language === 'fr'
        ? 'L\'huile d\'argan d\'Imlil Bio est fantastique ! Je l\'utilise pour ma peau et mes cheveux et les résultats sont incroyables. Qualité exceptionnelle et prix raisonnable.'
        : 'The argan oil from Imlil Bio is fantastic! I use it for my skin and hair and the results are amazing. Exceptional quality and reasonable price.',
      product: language === 'ar' ? 'زيت الأركان' : language === 'fr' ? 'Huile d\'Argan' : 'Argan Oil',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=150&q=80',
      verified: true
    },
    {
      id: 2,
      name: language === 'ar' ? 'أحمد بنعلي' : language === 'fr' ? 'Ahmed Benali' : 'Ahmed Benali',
      location: language === 'ar' ? 'الدار البيضاء، المغرب' : language === 'fr' ? 'Casablanca, Maroc' : 'Casablanca, Morocco',
      rating: 5,
      comment: language === 'ar'
        ? 'العسل الطبيعي من إمليل بيو له طعم رائع ونقي. أشتريه دائماً لعائلتي. منتج أصيل ومضمون.'
        : language === 'fr'
        ? 'Le miel naturel d\'Imlil Bio a un goût merveilleux et pur. Je l\'achète toujours pour ma famille. Produit authentique et garanti.'
        : 'The natural honey from Imlil Bio has a wonderful and pure taste. I always buy it for my family. Authentic and guaranteed product.',
      product: language === 'ar' ? 'العسل الطبيعي' : language === 'fr' ? 'Miel Naturel' : 'Natural Honey',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&w=150&q=80',
      verified: true
    },
    {
      id: 3,
      name: language === 'ar' ? 'خديجة المرابط' : language === 'fr' ? 'Khadija Mourabit' : 'Khadija Mourabit',
      location: language === 'ar' ? 'مراكش، المغرب' : language === 'fr' ? 'Marrakech, Maroc' : 'Marrakech, Morocco',
      rating: 5,
      comment: language === 'ar'
        ? 'الأملو التقليدي لذيذ جداً! يذكرني بطعم الطفولة. جودة ممتازة وتغليف أنيق.'
        : language === 'fr'
        ? 'L\'amlou traditionnel est délicieux ! Il me rappelle le goût de l\'enfance. Excellente qualité et emballage élégant.'
        : 'The traditional amlou is delicious! It reminds me of childhood flavors. Excellent quality and elegant packaging.',
      product: language === 'ar' ? 'الأملو التقليدي' : language === 'fr' ? 'Amlou Traditionnel' : 'Traditional Amlou',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&w=150&q=80',
      verified: true
    },
    {
      id: 4,
      name: language === 'ar' ? 'يوسف الإدريسي' : language === 'fr' ? 'Youssef Idrissi' : 'Youssef Idrissi',
      location: language === 'ar' ? 'فاس، المغرب' : language === 'fr' ? 'Fès, Maroc' : 'Fes, Morocco',
      rating: 4,
      comment: language === 'ar'
        ? 'خدمة ممتازة وتوصيل سريع. المنتجات طبيعية ونقية كما هو موعود. أنصح بشدة!'
        : language === 'fr'
        ? 'Service excellent et livraison rapide. Les produits sont naturels et purs comme promis. Je recommande vivement !'
        : 'Excellent service and fast delivery. Products are natural and pure as promised. Highly recommend!',
      product: language === 'ar' ? 'مجموعة متنوعة' : language === 'fr' ? 'Variété de produits' : 'Variety of products',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80',
      verified: true
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index: number) => {
    setCurrentTestimonial(index);
  };

  const currentData = testimonials[currentTestimonial];

  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 to-gray-800 text-white">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 25,
            duration: 0.8
          }}
          className={`text-center mb-16 ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            {t('testimonials.title')}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('testimonials.subtitle')}
          </p>
        </motion.div>

        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentTestimonial}
              initial={{
                opacity: 0,
                x: isRTL ? -60 : 60,
                scale: 0.95
              }}
              animate={{
                opacity: 1,
                x: 0,
                scale: 1
              }}
              exit={{
                opacity: 0,
                x: isRTL ? 60 : -60,
                scale: 0.95
              }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 25,
                duration: 0.6
              }}
              className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 md:p-12 border border-white/20 shadow-2xl"
            >
              <motion.div
                className="flex items-center justify-center mb-8"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  delay: 0.2,
                  type: "spring",
                  stiffness: 400,
                  damping: 20
                }}
              >
                <Quote className="h-12 w-12 text-golden opacity-50" />
              </motion.div>

              <blockquote className={`text-lg md:text-xl leading-relaxed mb-8 ${isRTL ? 'text-right' : 'text-left'}`}>
                "{currentData.comment}"
              </blockquote>

              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''} gap-4 mb-6`}>
                <img
                  src={currentData.avatar}
                  alt={currentData.name}
                  className="w-16 h-16 rounded-full object-cover border-2 border-golden"
                />
                <div className={isRTL ? 'text-right' : 'text-left'}>
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold text-lg">{currentData.name}</h4>
                    {currentData.verified && (
                      <div className="bg-green-500 rounded-full p-1">
                        <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <p className="text-gray-300">{currentData.location}</p>
                  <p className="text-golden text-sm">{currentData.product}</p>
                </div>
              </div>

              {/* Rating */}
              <div className={`flex items-center gap-2 ${isRTL ? 'justify-end' : 'justify-start'}`}>
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < currentData.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-400'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-gray-300">({currentData.rating}/5)</span>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex items-center justify-center mt-8 gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={prevTestimonial}
              className="text-white hover:bg-white/20"
            >
              {isRTL ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
            </Button>

            <div className="flex gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentTestimonial
                      ? 'bg-golden scale-125'
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                />
              ))}
            </div>

            <Button
              variant="ghost"
              size="icon"
              onClick={nextTestimonial}
              className="text-white hover:bg-white/20"
            >
              {isRTL ? <ChevronLeft className="h-5 w-5" /> : <ChevronRight className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20"
        >
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-golden mb-2">500+</div>
            <div className="text-gray-300">{t('testimonials.stats.customers')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-golden mb-2">4.8</div>
            <div className="text-gray-300">{t('testimonials.stats.rating')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-golden mb-2">98%</div>
            <div className="text-gray-300">{t('testimonials.stats.satisfaction')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-golden mb-2">24h</div>
            <div className="text-gray-300">{t('testimonials.stats.delivery')}</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CustomerTestimonials;
