
import React from 'react';
import { motion } from 'framer-motion';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useLanguage } from '@/contexts/LanguageContext';

interface ProductTabsProps {
  description?: string;
  benefits?: string[];
  itemVariants: any;
}

const ProductTabs: React.FC<ProductTabsProps> = ({
  description,
  benefits = [],
  itemVariants
}) => {
  const { language, isRTL } = useLanguage();

  return (
    <motion.div 
      variants={itemVariants}
      className="bg-gray-50/50 rounded-lg p-1"
    >
      <Tabs defaultValue="description" className="w-full">
        <TabsList className="w-full grid grid-cols-2">
          <TabsTrigger value="description">
            {language === 'ar' ? 'الوصف' : language === 'fr' ? 'Description' : 'Description'}
          </TabsTrigger>
          <TabsTrigger value="benefits">
            {language === 'ar' ? 'الفوائد' : language === 'fr' ? 'Bienfaits' : 'Benefits'}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="description" className={`mt-4 ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="text-gray-700"
          >
            {description || (language === 'ar' 
              ? 'لا يوجد وصف متاح لهذا المنتج.'
              : language === 'fr' 
                ? 'Aucune description disponible pour ce produit.'
                : 'No description available for this product.')}
          </motion.p>
        </TabsContent>
        
        <TabsContent value="benefits" className="mt-4">
          {benefits && benefits.length > 0 ? (
            <ul className={`space-y-2 list-disc ${isRTL ? 'mr-5' : 'ml-5'} ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
              {benefits.map((benefit, index) => (
                <motion.li 
                  key={index}
                  initial={{ opacity: 0, x: isRTL ? 10 : -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 + index * 0.1 }}
                  className="text-gray-700"
                >
                  {benefit}
                </motion.li>
              ))}
            </ul>
          ) : (
            <p className={`text-gray-500 ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
              {language === 'ar' 
                ? 'لا توجد فوائد محددة لهذا المنتج.'
                : language === 'fr' 
                  ? 'Aucun bienfait spécifié pour ce produit.'
                  : 'No benefits specified for this product.'}
            </p>
          )}
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default ProductTabs;
