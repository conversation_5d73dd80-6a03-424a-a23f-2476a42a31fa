
import React from 'react';
import { motion } from 'framer-motion';
import { Info } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface UsageTipsProps {
  itemVariants: any;
}

const UsageTips: React.FC<UsageTipsProps> = ({ itemVariants }) => {
  const { language } = useLanguage();

  return (
    <motion.div 
      variants={itemVariants}
      className="mt-6 p-4 bg-olive/5 rounded-lg border border-olive/20"
    >
      <div className="flex items-center gap-2 mb-2">
        <Info className="h-4 w-4 text-olive" />
        <h3 className={`font-medium ${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
          {language === 'ar' ? 'نصائح الاستخدام' : language === 'fr' ? 'Conseils d\'utilisation' : 'Usage Tips'}
        </h3>
      </div>
      <p className={`text-sm text-gray-600 ${language === 'ar' ? 'font-arabic text-right' : 'font-body'}`}>
        {language === 'ar' 
          ? 'للشعر: ضعي بضع قطرات على شعرك الرطب قبل التجفيف. للبشرة: دلكي 2-3 قطرات على وجهك النظيف.'
          : language === 'fr'
            ? 'Pour les cheveux: Appliquez quelques gouttes sur cheveux humides avant le séchage. Pour la peau: Massez 2-3 gouttes sur le visage propre.'
            : 'For hair: Apply a few drops to damp hair before drying. For skin: Massage 2-3 drops onto clean face.'}
      </p>
    </motion.div>
  );
};

export default UsageTips;
