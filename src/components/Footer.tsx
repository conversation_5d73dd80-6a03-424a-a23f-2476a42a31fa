
import React from 'react';
import { motion } from 'framer-motion';
import { useLanguage } from '@/contexts/LanguageContext';
import { Instagram, Facebook } from 'lucide-react';

const Footer: React.FC = () => {
  const { t, language, isRTL } = useLanguage();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 25
      }
    }
  };

  const footerLinks = [
    { key: 'footer.contact', href: '#contact' },
    { key: 'footer.privacy', href: '#privacy' },
    { key: 'footer.terms', href: '#terms' },
  ];

  const paymentMethods = [
    { icon: 'paypal', name: 'PayPal' },
    { icon: 'stripe', name: '<PERSON><PERSON>' },
    { icon: 'cash-on-delivery', name: t('footer.cod') },
  ];

  return (
    <motion.footer
      className="bg-olive text-white pt-12 pb-6"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={containerVariants}
    >
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          variants={containerVariants}
        >
          {/* Company Info */}
          <motion.div variants={itemVariants}>
            <motion.div
              className={`flex items-center mb-4 ${isRTL ? 'justify-end' : 'justify-start'}`}
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 20 }}
            >
              <motion.div
                className="w-10 h-10 rounded-full bg-white text-olive flex items-center justify-center font-title text-xl cursor-pointer"
                whileHover={{
                  rotate: 360,
                  scale: 1.1
                }}
                transition={{ duration: 0.5 }}
              >
                IB
              </motion.div>
              <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-xl font-bold ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
                {language === 'ar' ? 'إيميل بيو' : 'Imil Bio'}
              </span>
            </motion.div>
            <p className={`mb-4 ${language === 'ar' ? 'font-arabic' : 'font-body'} ${isRTL ? 'text-right' : 'text-left'}`}>
              {language === 'ar' 
                ? 'منتجات مغربية عضوية عالية الجودة'
                : language === 'fr'
                  ? 'Produits Bio Marocains de haute qualité'
                  : 'High quality Moroccan organic products'}
            </p>
            <div className={`flex space-x-4 ${isRTL ? 'space-x-reverse justify-end' : 'justify-start'}`}>
              <motion.a
                href="#instagram"
                className="hover:text-sand transition duration-200"
                whileHover={{
                  scale: 1.2,
                  rotate: 15,
                  color: "#D4AF37"
                }}
                whileTap={{ scale: 0.9 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
              >
                <Instagram size={20} />
              </motion.a>
              <motion.a
                href="#facebook"
                className="hover:text-sand transition duration-200"
                whileHover={{
                  scale: 1.2,
                  rotate: -15,
                  color: "#D4AF37"
                }}
                whileTap={{ scale: 0.9 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
              >
                <Facebook size={20} />
              </motion.a>
            </div>
          </motion.div>

          {/* Links */}
          <div className={isRTL ? 'text-right' : 'text-left'}>
            <h3 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
              {language === 'ar' ? 'روابط سريعة' : language === 'fr' ? 'Liens Rapides' : 'Quick Links'}
            </h3>
            <ul className="space-y-2">
              {footerLinks.map((link) => (
                <li key={link.key}>
                  <a 
                    href={link.href} 
                    className={`${language === 'ar' ? 'font-arabic' : 'font-body'} hover:text-sand transition duration-200`}
                  >
                    {t(link.key)}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Payment Methods */}
          <div className={isRTL ? 'text-right' : 'text-left'}>
            <h3 className={`text-lg font-bold mb-4 ${language === 'ar' ? 'font-arabic' : 'font-title'}`}>
              {language === 'ar' ? 'طرق الدفع' : language === 'fr' ? 'Méthodes de paiement' : 'Payment Methods'}
            </h3>
            <div className="flex flex-wrap gap-2">
              {paymentMethods.map((method) => (
                <div 
                  key={method.name} 
                  className="bg-white text-olive px-3 py-1 rounded text-sm font-medium"
                >
                  <span className={`${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
                    {method.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Copyright */}
        <div className={`mt-12 pt-4 border-t border-white/20 text-sm ${isRTL ? 'text-right' : 'text-left'}`}>
          <p className={`${language === 'ar' ? 'font-arabic' : 'font-body'}`}>
            &copy; {new Date().getFullYear()} Imil Bio. {t('footer.rights')}
          </p>
        </div>
      </div>
    </motion.footer>
  );
};

export default Footer;
