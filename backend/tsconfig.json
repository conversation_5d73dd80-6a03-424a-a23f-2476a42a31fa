{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/controllers/*": ["./controllers/*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/middleware/*": ["./middleware/*"], "@/services/*": ["./services/*"], "@/config/*": ["./config/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}