#!/usr/bin/env node

/**
 * Script de migration pour :
 * 1. Créer les catégories de base avec support multilingue
 * 2. Mettre à jour les produits pour utiliser categoryId au lieu de category
 * 3. Corriger tous les chemins d'images pour utiliser la nouvelle structure
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Charger les variables d'environnement
dotenv.config();

// Schémas MongoDB simples
const multiLanguageStringSchema = new mongoose.Schema({
  ar: { type: String, required: true, trim: true },
  fr: { type: String, required: true, trim: true },
  en: { type: String, required: true, trim: true }
}, { _id: false });

const multiLanguageKeywordsSchema = new mongoose.Schema({
  ar: [{ type: String, trim: true }],
  fr: [{ type: String, trim: true }],
  en: [{ type: String, trim: true }]
}, { _id: false });

const seoSchema = new mongoose.Schema({
  title: multiLanguageStringSchema,
  description: multiLanguageStringSchema,
  keywords: multiLanguageKeywordsSchema
}, { _id: false });

const categorySchema = new mongoose.Schema({
  name: {
    type: multiLanguageStringSchema,
    required: [true, 'Category name is required'],
    index: true
  },
  slug: {
    type: multiLanguageStringSchema,
    required: [true, 'Category slug is required'],
    unique: true,
    index: true
  },
  description: {
    type: multiLanguageStringSchema,
    required: [true, 'Category description is required']
  },
  image: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^\/images\/categories\//.test(v);
      },
      message: 'Image path must start with /images/categories/'
    }
  },
  parentCategory: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  sortOrder: {
    type: Number,
    default: 0,
    index: true
  },
  seo: seoSchema
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

const Category = mongoose.model('Category', categorySchema);

// Schéma Product simplifié pour la migration
const productSchema = new mongoose.Schema({
  title: multiLanguageStringSchema,
  description: multiLanguageStringSchema,
  price: Number,
  originalPrice: Number,
  discountPercentage: Number,
  category: String, // Ancien champ
  categoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' }, // Nouveau champ
  subcategory: String, // Ancien champ
  subcategoryId: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' }, // Nouveau champ
  images: [String],
  tags: [String],
  isOrganic: Boolean,
  inStock: Boolean,
  stockQuantity: Number,
  weight: String,
  volume: String,
  ingredients: {
    ar: [String],
    fr: [String],
    en: [String]
  },
  benefits: {
    ar: [String],
    fr: [String],
    en: [String]
  },
  usage: multiLanguageStringSchema,
  origin: String,
  certifications: [String],
  isActive: Boolean,
  featured: Boolean,
  seoTitle: multiLanguageStringSchema,
  seoDescription: multiLanguageStringSchema,
  slug: multiLanguageStringSchema
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

const Product = mongoose.model('Product', productSchema);

// Données des catégories de base
const baseCategories = [
  {
    name: {
      ar: 'زيت الأركان',
      fr: 'Huile d\'Argan',
      en: 'Argan Oil'
    },
    slug: {
      ar: 'زيت-الأركان',
      fr: 'huile-argan',
      en: 'argan-oil'
    },
    description: {
      ar: 'زيت الأركان الطبيعي من المغرب، مثالي للعناية بالبشرة والشعر',
      fr: 'Huile d\'argan naturelle du Maroc, idéale pour les soins de la peau et des cheveux',
      en: 'Natural argan oil from Morocco, ideal for skin and hair care'
    },
    image: '/images/categories/argan-oil-category.jpg',
    sortOrder: 1,
    oldCategory: 'argan'
  },
  {
    name: {
      ar: 'العسل',
      fr: 'Miel',
      en: 'Honey'
    },
    slug: {
      ar: 'العسل',
      fr: 'miel',
      en: 'honey'
    },
    description: {
      ar: 'عسل طبيعي من الجبال المغربية، غني بالفوائد الصحية',
      fr: 'Miel naturel des montagnes marocaines, riche en bienfaits pour la santé',
      en: 'Natural honey from Moroccan mountains, rich in health benefits'
    },
    image: '/images/categories/honey-category.jpg',
    sortOrder: 2,
    oldCategory: 'honey'
  },
  {
    name: {
      ar: 'أملو',
      fr: 'Amlou',
      en: 'Amlou'
    },
    slug: {
      ar: 'أملو',
      fr: 'amlou',
      en: 'amlou'
    },
    description: {
      ar: 'أملو تقليدي مغربي مصنوع من اللوز وزيت الأركان والعسل',
      fr: 'Amlou traditionnel marocain fait d\'amandes, d\'huile d\'argan et de miel',
      en: 'Traditional Moroccan amlou made from almonds, argan oil and honey'
    },
    image: '/images/categories/amlou-category.jpg',
    sortOrder: 3,
    oldCategory: 'amlou'
  }
];

// Mapping des anciens chemins d'images vers les nouveaux
const imagePathMappings = {
  // Images de produits
  'amlou-traditionnel.jpg': '/images/products/amlou-traditionnel-1.jpg',
  'miel-montagne.jpg': '/images/products/miel-montagne-1.jpg',
  'miel-acacia.jpg': '/images/products/miel-acacia-1.jpg',
  'huile-argan-cosmetique.jpg': '/images/products/huile-argan-cosmetique-1.jpg',
  'huile-argan-culinaire.jpg': '/images/products/huile-argan-culinaire-1.jpg',
  
  // Ajouter d'autres mappings si nécessaire
  '/amlou-traditionnel.jpg': '/images/products/amlou-traditionnel-1.jpg',
  '/miel-montagne.jpg': '/images/products/miel-montagne-1.jpg',
  '/miel-acacia.jpg': '/images/products/miel-acacia-1.jpg',
  '/huile-argan-cosmetique.jpg': '/images/products/huile-argan-cosmetique-1.jpg',
  '/huile-argan-culinaire.jpg': '/images/products/huile-argan-culinaire-1.jpg'
};

async function connectToDatabase() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/imlil-bio-boutique';
    await mongoose.connect(mongoUri);
    console.log('✅ Connexion à MongoDB réussie');
  } catch (error) {
    console.error('❌ Erreur de connexion à MongoDB:', error);
    process.exit(1);
  }
}

async function createCategories() {
  console.log('\n🏷️ Création des catégories...');
  
  const categoryMap = new Map();
  
  for (const categoryData of baseCategories) {
    try {
      // Vérifier si la catégorie existe déjà
      const existingCategory = await Category.findOne({ 'slug.fr': categoryData.slug.fr });
      
      if (existingCategory) {
        console.log(`⚠️ Catégorie "${categoryData.name.fr}" existe déjà`);
        categoryMap.set(categoryData.oldCategory, existingCategory._id);
        continue;
      }
      
      // Créer la nouvelle catégorie
      const category = new Category(categoryData);
      await category.save();
      
      categoryMap.set(categoryData.oldCategory, category._id);
      console.log(`✅ Catégorie "${categoryData.name.fr}" créée avec ID: ${category._id}`);
      
    } catch (error) {
      console.error(`❌ Erreur lors de la création de la catégorie "${categoryData.name.fr}":`, error);
    }
  }
  
  return categoryMap;
}

async function updateProductImages() {
  console.log('\n🖼️ Mise à jour des chemins d\'images des produits...');
  
  try {
    const products = await Product.find({});
    console.log(`📦 ${products.length} produits trouvés`);
    
    for (const product of products) {
      let updated = false;
      
      // Mettre à jour les images du produit
      if (product.images && product.images.length > 0) {
        const updatedImages = product.images.map(imagePath => {
          // Nettoyer le chemin d'image
          const cleanPath = imagePath.replace(/^\/+/, '');
          
          // Vérifier si le chemin existe dans le mapping
          if (imagePathMappings[cleanPath] || imagePathMappings[imagePath]) {
            updated = true;
            return imagePathMappings[cleanPath] || imagePathMappings[imagePath];
          }
          
          // Si le chemin ne commence pas par /images/, l'ajouter
          if (!imagePath.startsWith('/images/')) {
            updated = true;
            return `/images/products/${cleanPath}`;
          }
          
          return imagePath;
        });
        
        if (updated) {
          product.images = updatedImages;
        }
      }
      
      if (updated) {
        await product.save();
        console.log(`✅ Images mises à jour pour le produit: ${product.title.fr}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour des images:', error);
  }
}

async function updateProductCategories(categoryMap) {
  console.log('\n🔄 Mise à jour des références de catégories des produits...');
  
  try {
    const products = await Product.find({});
    console.log(`📦 ${products.length} produits trouvés`);
    
    for (const product of products) {
      // Vérifier si le produit a encore l'ancien champ category
      if (product.category && typeof product.category === 'string') {
        const categoryId = categoryMap.get(product.category);
        
        if (categoryId) {
          // Mettre à jour avec le nouveau champ categoryId
          await Product.updateOne(
            { _id: product._id },
            { 
              $set: { categoryId: categoryId },
              $unset: { category: 1, subcategory: 1 }
            }
          );
          
          console.log(`✅ Produit "${product.title.fr}" mis à jour: ${product.category} -> ${categoryId}`);
        } else {
          console.log(`⚠️ Catégorie "${product.category}" non trouvée pour le produit "${product.title.fr}"`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la mise à jour des catégories:', error);
  }
}

async function verifyMigration() {
  console.log('\n🔍 Vérification de la migration...');
  
  try {
    // Vérifier les catégories
    const categoriesCount = await Category.countDocuments();
    console.log(`📊 Catégories créées: ${categoriesCount}`);
    
    // Vérifier les produits avec categoryId
    const productsWithCategoryId = await Product.countDocuments({ categoryId: { $exists: true } });
    const productsWithOldCategory = await Product.countDocuments({ category: { $exists: true } });
    
    console.log(`📊 Produits avec categoryId: ${productsWithCategoryId}`);
    console.log(`📊 Produits avec ancien champ category: ${productsWithOldCategory}`);
    
    // Vérifier les images
    const productsWithCorrectImages = await Product.countDocuments({ 
      images: { $regex: '^/images/products/' } 
    });
    console.log(`📊 Produits avec chemins d'images corrects: ${productsWithCorrectImages}`);
    
  } catch (error) {
    console.error('❌ Erreur lors de la vérification:', error);
  }
}

async function main() {
  console.log('🚀 Début de la migration des catégories et images...\n');
  
  await connectToDatabase();
  
  try {
    // 1. Créer les catégories
    const categoryMap = await createCategories();
    
    // 2. Mettre à jour les chemins d'images
    await updateProductImages();
    
    // 3. Mettre à jour les références de catégories
    await updateProductCategories(categoryMap);
    
    // 4. Vérifier la migration
    await verifyMigration();
    
    console.log('\n🎉 Migration terminée avec succès !');
    
  } catch (error) {
    console.error('\n❌ Erreur lors de la migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnexion de MongoDB');
  }
}

// Exécuter le script
main().catch(console.error);
