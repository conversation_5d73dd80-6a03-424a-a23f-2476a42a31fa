{"name": "imlil-bio-boutique-backend", "version": "1.0.0", "description": "Backend API for Imlil Bio Boutique e-commerce application", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["ecommerce", "api", "mongodb", "express", "typescript"], "author": "Imlil Bio Boutique", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "rate-limiter-flexible": "^2.4.2", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}}