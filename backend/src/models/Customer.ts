import mongoose, { Schema } from 'mongoose';
import { I<PERSON>ustomer } from '../types/index.js';

const customerSchema = new Schema<ICustomer>({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ],
    index: true
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [
      /^(\+212|0)[5-7][0-9]{8}$/,
      'Please provide a valid Moroccan phone number'
    ],
    index: true
  },
  age: {
    type: String,
    trim: true,
    enum: ['18-25', '26-35', '36-45', '46-55', '56-65', '65+']
  },
  dateOfBirth: {
    type: Date,
    validate: {
      validator: function(value: Date) {
        if (!value) return true;
        const today = new Date();
        const age = today.getFullYear() - value.getFullYear();
        return age >= 13 && age <= 120;
      },
      message: 'Age must be between 13 and 120 years'
    }
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  emailVerified: {
    type: Boolean,
    default: false,
    index: true
  },
  phoneVerified: {
    type: Boolean,
    default: false,
    index: true
  },
  preferredLanguage: {
    type: String,
    enum: ['ar', 'fr', 'en'],
    default: 'ar',
    index: true
  },
  addresses: [{
    type: Schema.Types.ObjectId,
    ref: 'Address'
  }],
  orders: [{
    type: Schema.Types.ObjectId,
    ref: 'Order'
  }],
  totalOrders: {
    type: Number,
    default: 0,
    min: [0, 'Total orders cannot be negative']
  },
  totalSpent: {
    type: Number,
    default: 0,
    min: [0, 'Total spent cannot be negative']
  },
  lastOrderDate: {
    type: Date,
    index: true
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for better query performance
customerSchema.index({ email: 1, isActive: 1 });
customerSchema.index({ phone: 1, isActive: 1 });
customerSchema.index({ totalSpent: -1 });
customerSchema.index({ lastOrderDate: -1 });
customerSchema.index({ createdAt: -1 });

// Virtual for full name
customerSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for customer tier based on total spent
customerSchema.virtual('customerTier').get(function() {
  if (this.totalSpent >= 5000) return 'platinum';
  if (this.totalSpent >= 2000) return 'gold';
  if (this.totalSpent >= 500) return 'silver';
  return 'bronze';
});

// Virtual for customer age from date of birth
customerSchema.virtual('calculatedAge').get(function() {
  if (!this.dateOfBirth) return null;
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for average order value
customerSchema.virtual('averageOrderValue').get(function() {
  if (this.totalOrders === 0) return 0;
  return Math.round((this.totalSpent / this.totalOrders) * 100) / 100;
});

// Pre-save middleware to update calculated fields
customerSchema.pre('save', function(next) {
  // Ensure email is lowercase
  if (this.email) {
    this.email = this.email.toLowerCase();
  }
  
  // Clean phone number
  if (this.phone) {
    this.phone = this.phone.replace(/\s+/g, '');
  }
  
  next();
});

// Static method to find customers by tier
customerSchema.statics.findByTier = function(tier: string) {
  let minSpent = 0;
  let maxSpent = Infinity;
  
  switch (tier) {
    case 'bronze':
      maxSpent = 499;
      break;
    case 'silver':
      minSpent = 500;
      maxSpent = 1999;
      break;
    case 'gold':
      minSpent = 2000;
      maxSpent = 4999;
      break;
    case 'platinum':
      minSpent = 5000;
      break;
  }
  
  const query: any = { 
    isActive: true,
    totalSpent: { $gte: minSpent }
  };
  
  if (maxSpent !== Infinity) {
    query.totalSpent.$lte = maxSpent;
  }
  
  return this.find(query);
};

// Static method to search customers
customerSchema.statics.searchCustomers = function(query: string) {
  const searchRegex = new RegExp(query, 'i');
  return this.find({
    $or: [
      { firstName: searchRegex },
      { lastName: searchRegex },
      { email: searchRegex },
      { phone: searchRegex }
    ],
    isActive: true
  });
};

// Instance method to add order
customerSchema.methods.addOrder = function(orderTotal: number) {
  this.totalOrders += 1;
  this.totalSpent += orderTotal;
  this.lastOrderDate = new Date();
  return this.save();
};

// Instance method to get customer summary
customerSchema.methods.getSummary = function() {
  return {
    id: this._id,
    fullName: this.fullName,
    email: this.email,
    phone: this.phone,
    tier: this.customerTier,
    totalOrders: this.totalOrders,
    totalSpent: this.totalSpent,
    averageOrderValue: this.averageOrderValue,
    lastOrderDate: this.lastOrderDate,
    isActive: this.isActive,
    emailVerified: this.emailVerified,
    phoneVerified: this.phoneVerified
  };
};

export const Customer = mongoose.model<ICustomer>('Customer', customerSchema);
