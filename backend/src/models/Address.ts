import mongoose, { Schema } from 'mongoose';
import { IAddress } from '../types/index.js';

const addressSchema = new Schema<IAddress>({
  customerId: {
    type: Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'Customer ID is required'],
    index: true
  },
  type: {
    type: String,
    enum: ['shipping', 'billing', 'both'],
    default: 'shipping',
    index: true
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  streetAddress: {
    type: String,
    required: [true, 'Street address is required'],
    trim: true,
    maxlength: [200, 'Street address cannot exceed 200 characters']
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true,
    maxlength: [100, 'City cannot exceed 100 characters'],
    index: true
  },
  postalCode: {
    type: String,
    required: [true, 'Postal code is required'],
    trim: true,
    match: [/^\d{5}$/, 'Please provide a valid 5-digit postal code'],
    index: true
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    default: 'Morocco',
    maxlength: [100, 'Country cannot exceed 100 characters']
  },
  region: {
    type: String,
    required: [true, 'Region is required'],
    trim: true,
    maxlength: [100, 'Region cannot exceed 100 characters'],
    index: true
  },
  isDefault: {
    type: Boolean,
    default: false,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
addressSchema.index({ customerId: 1, isActive: 1 });
addressSchema.index({ customerId: 1, isDefault: 1 });
addressSchema.index({ customerId: 1, type: 1 });
addressSchema.index({ city: 1, region: 1 });

// Virtual for full name
addressSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for formatted address
addressSchema.virtual('formattedAddress').get(function() {
  return `${this.streetAddress}, ${this.city} ${this.postalCode}, ${this.region}, ${this.country}`;
});

// Virtual for short address (for display purposes)
addressSchema.virtual('shortAddress').get(function() {
  return `${this.city}, ${this.region}`;
});

// Pre-save middleware to ensure only one default address per customer
addressSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default flag from other addresses of the same customer
    await this.constructor.updateMany(
      { 
        customerId: this.customerId, 
        _id: { $ne: this._id },
        type: { $in: [this.type, 'both'] }
      },
      { $set: { isDefault: false } }
    );
  }
  
  next();
});

// Pre-remove middleware to handle default address deletion
addressSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  if (this.isDefault) {
    // Find another address of the same type to make default
    const nextAddress = await this.constructor.findOne({
      customerId: this.customerId,
      _id: { $ne: this._id },
      type: { $in: [this.type, 'both'] },
      isActive: true
    });
    
    if (nextAddress) {
      nextAddress.isDefault = true;
      await nextAddress.save();
    }
  }
  
  next();
});

// Static method to find addresses by customer
addressSchema.statics.findByCustomer = function(customerId: string, type?: string) {
  const query: any = { 
    customerId, 
    isActive: true 
  };
  
  if (type) {
    query.type = { $in: [type, 'both'] };
  }
  
  return this.find(query).sort({ isDefault: -1, createdAt: -1 });
};

// Static method to find default address
addressSchema.statics.findDefaultByCustomer = function(customerId: string, type: string = 'shipping') {
  return this.findOne({
    customerId,
    type: { $in: [type, 'both'] },
    isDefault: true,
    isActive: true
  });
};

// Static method to get addresses by region for analytics
addressSchema.statics.getAddressesByRegion = function() {
  return this.aggregate([
    { $match: { isActive: true } },
    { $group: { _id: '$region', count: { $sum: 1 } } },
    { $sort: { count: -1 } }
  ]);
};

// Instance method to set as default
addressSchema.methods.setAsDefault = function() {
  this.isDefault = true;
  return this.save();
};

// Instance method to validate address format
addressSchema.methods.validateAddress = function() {
  const errors: string[] = [];
  
  if (!this.streetAddress || this.streetAddress.length < 10) {
    errors.push('Street address must be at least 10 characters long');
  }
  
  if (!this.postalCode || !/^\d{5}$/.test(this.postalCode)) {
    errors.push('Postal code must be exactly 5 digits');
  }
  
  if (!this.city || this.city.length < 2) {
    errors.push('City must be at least 2 characters long');
  }
  
  if (!this.region || this.region.length < 2) {
    errors.push('Region must be at least 2 characters long');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Instance method to get shipping cost (placeholder for future implementation)
addressSchema.methods.calculateShippingCost = function(orderTotal: number = 0) {
  // Basic shipping cost calculation based on region
  const regionShippingCosts: { [key: string]: number } = {
    'Casablanca': 25,
    'Rabat': 30,
    'Marrakech': 35,
    'Fès': 40,
    'Tanger': 45,
    'Agadir': 50,
    'Oujda': 55,
    'Kenitra': 35,
    'Tétouan': 45,
    'Safi': 40
  };
  
  const baseCost = regionShippingCosts[this.region] || 60; // Default for other regions
  
  // Free shipping for orders over 500 MAD
  if (orderTotal >= 500) {
    return 0;
  }
  
  return baseCost;
};

export const Address = mongoose.model<IAddress>('Address', addressSchema);
