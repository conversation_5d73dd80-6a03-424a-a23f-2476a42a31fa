import mongoose, { Schema } from 'mongoose';
import { IOrder, IOrderItem, OrderStatus, PaymentStatus, PaymentMethod } from '../types/index.js';

const orderItemSchema = new Schema<IOrderItem>({
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required']
  },
  productTitle: {
    type: String,
    required: [true, 'Product title is required'],
    trim: true
  },
  productImage: {
    type: String,
    required: [true, 'Product image is required'],
    trim: true
  },
  productCategory: {
    type: String,
    required: [true, 'Product category is required'],
    enum: ['argan', 'honey', 'amlou']
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price must be positive']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price must be positive']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1']
  },
  total: {
    type: Number,
    required: [true, 'Total is required'],
    min: [0, 'Total must be positive']
  }
}, { _id: false });

const addressSchema = new Schema({
  streetAddress: { type: String, required: true, trim: true },
  city: { type: String, required: true, trim: true },
  postalCode: { type: String, required: true, trim: true },
  country: { type: String, required: true, trim: true, default: 'Morocco' },
  region: { type: String, required: true, trim: true }
}, { _id: false });

const customerInfoSchema = new Schema({
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  phone: { type: String, required: true, trim: true }
}, { _id: false });

const orderSchema = new Schema<IOrder>({
  orderId: {
    type: String,
    required: [true, 'Order ID is required'],
    unique: true,
    trim: true
  },
  customerId: {
    type: Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'Customer ID is required'],
    index: true
  },
  customerInfo: {
    type: customerInfoSchema,
    required: [true, 'Customer information is required']
  },
  shippingAddress: {
    type: addressSchema,
    required: [true, 'Shipping address is required']
  },
  billingAddress: {
    type: addressSchema
  },
  items: {
    type: [orderItemSchema],
    required: [true, 'Order items are required'],
    validate: {
      validator: function(items: IOrderItem[]) {
        return items && items.length > 0;
      },
      message: 'Order must contain at least one item'
    }
  },
  subtotal: {
    type: Number,
    required: [true, 'Subtotal is required'],
    min: [0, 'Subtotal must be positive']
  },
  shippingFee: {
    type: Number,
    required: [true, 'Shipping fee is required'],
    min: [0, 'Shipping fee must be positive'],
    default: 0
  },
  tax: {
    type: Number,
    min: [0, 'Tax must be positive'],
    default: 0
  },
  discount: {
    type: Number,
    min: [0, 'Discount must be positive'],
    default: 0
  },
  total: {
    type: Number,
    required: [true, 'Total is required'],
    min: [0, 'Total must be positive']
  },
  paymentMethod: {
    type: String,
    required: [true, 'Payment method is required'],
    enum: ['cash_on_delivery', 'credit_card', 'bank_transfer', 'paypal'],
    index: true
  },
  paymentStatus: {
    type: String,
    required: [true, 'Payment status is required'],
    enum: ['pending', 'paid', 'failed', 'refunded', 'partially_refunded'],
    default: 'pending',
    index: true
  },
  orderStatus: {
    type: String,
    required: [true, 'Order status is required'],
    enum: ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'],
    default: 'pending',
    index: true
  },
  orderDate: {
    type: Date,
    required: [true, 'Order date is required'],
    default: Date.now,
    index: true
  },
  estimatedDelivery: {
    type: Date,
    index: true
  },
  actualDelivery: {
    type: Date,
    index: true
  },
  trackingNumber: {
    type: String,
    trim: true,
    index: true
  },
  notes: {
    type: String,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  adminNotes: {
    type: String,
    maxlength: [500, 'Admin notes cannot exceed 500 characters']
  },
  emailSent: {
    type: Boolean,
    default: false,
    index: true
  },
  invoiceSent: {
    type: Boolean,
    default: false,
    index: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
orderSchema.index({ orderId: 1 });
orderSchema.index({ customerId: 1, orderDate: -1 });
orderSchema.index({ orderStatus: 1, orderDate: -1 });
orderSchema.index({ paymentStatus: 1, orderDate: -1 });
orderSchema.index({ 'shippingAddress.region': 1 });
orderSchema.index({ orderDate: -1 });
orderSchema.index({ total: -1 });

// Virtual for customer full name
orderSchema.virtual('customerFullName').get(function() {
  return `${this.customerInfo.firstName} ${this.customerInfo.lastName}`;
});

// Virtual for order age in days
orderSchema.virtual('orderAge').get(function() {
  const now = new Date();
  const orderDate = new Date(this.orderDate);
  const diffTime = Math.abs(now.getTime() - orderDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Virtual for total items count
orderSchema.virtual('totalItems').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for order summary
orderSchema.virtual('summary').get(function() {
  return {
    orderId: this.orderId,
    customerName: this.customerFullName,
    totalItems: this.totalItems,
    total: this.total,
    status: this.orderStatus,
    paymentStatus: this.paymentStatus,
    orderDate: this.orderDate
  };
});

// Pre-save middleware to calculate totals and set estimated delivery
orderSchema.pre('save', function(next) {
  // Calculate subtotal from items
  this.subtotal = this.items.reduce((total, item) => total + item.total, 0);
  
  // Calculate total
  this.total = this.subtotal + this.shippingFee + (this.tax || 0) - (this.discount || 0);
  
  // Set estimated delivery (7 days from order date for new orders)
  if (this.isNew && !this.estimatedDelivery) {
    const estimatedDate = new Date(this.orderDate);
    estimatedDate.setDate(estimatedDate.getDate() + 7);
    this.estimatedDelivery = estimatedDate;
  }
  
  // Set actual delivery date when status changes to delivered
  if (this.isModified('orderStatus') && this.orderStatus === 'delivered' && !this.actualDelivery) {
    this.actualDelivery = new Date();
  }
  
  next();
});

// Static method to generate unique order ID
orderSchema.statics.generateOrderId = async function() {
  const prefix = 'IML';
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  
  let orderId = `${prefix}${timestamp}${random}`;
  
  // Ensure uniqueness
  while (await this.findOne({ orderId })) {
    const newRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    orderId = `${prefix}${timestamp}${newRandom}`;
  }
  
  return orderId;
};

// Static method to find orders by status
orderSchema.statics.findByStatus = function(status: OrderStatus) {
  return this.find({ orderStatus: status }).sort({ orderDate: -1 });
};

// Static method to find orders by customer
orderSchema.statics.findByCustomer = function(customerId: string) {
  return this.find({ customerId }).sort({ orderDate: -1 });
};

// Static method to get orders by date range
orderSchema.statics.findByDateRange = function(startDate: Date, endDate: Date) {
  return this.find({
    orderDate: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ orderDate: -1 });
};

// Instance method to update status
orderSchema.methods.updateStatus = function(newStatus: OrderStatus, adminNotes?: string) {
  this.orderStatus = newStatus;
  if (adminNotes) {
    this.adminNotes = adminNotes;
  }
  return this.save();
};

// Instance method to update payment status
orderSchema.methods.updatePaymentStatus = function(newStatus: PaymentStatus) {
  this.paymentStatus = newStatus;
  return this.save();
};

// Instance method to add tracking number
orderSchema.methods.addTracking = function(trackingNumber: string) {
  this.trackingNumber = trackingNumber;
  if (this.orderStatus === 'confirmed' || this.orderStatus === 'processing') {
    this.orderStatus = 'shipped';
  }
  return this.save();
};

// Instance method to calculate refund amount
orderSchema.methods.calculateRefund = function(refundShipping: boolean = false) {
  let refundAmount = this.subtotal + (this.tax || 0) - (this.discount || 0);
  
  if (refundShipping) {
    refundAmount += this.shippingFee;
  }
  
  return Math.max(0, refundAmount);
};

export const Order = mongoose.model<IOrder>('Order', orderSchema);
