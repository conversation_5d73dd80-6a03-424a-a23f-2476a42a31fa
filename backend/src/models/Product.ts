import mongoose, { Schema } from 'mongoose';
import { IProduct } from '../types/index.js';

const multiLanguageStringSchema = new Schema({
  ar: { type: String, required: true, trim: true },
  fr: { type: String, required: true, trim: true },
  en: { type: String, required: true, trim: true }
}, { _id: false });

const multiLanguageArraySchema = new Schema({
  ar: [{ type: String, trim: true }],
  fr: [{ type: String, trim: true }],
  en: [{ type: String, trim: true }]
}, { _id: false });

const productSchema = new Schema<IProduct>({
  title: {
    type: multiLanguageStringSchema,
    required: true,
    index: true
  },
  description: {
    type: multiLanguageStringSchema,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price must be positive'],
    index: true
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price must be positive'],
    validate: {
      validator: function(this: IProduct, value: number) {
        return !value || value >= this.price;
      },
      message: 'Original price must be greater than or equal to current price'
    }
  },
  discountPercentage: {
    type: Number,
    min: [0, 'Discount percentage must be positive'],
    max: [100, 'Discount percentage cannot exceed 100%']
  },
  categoryId: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: true,
    index: true
  },
  subcategoryId: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    index: true
  },
  images: [{
    type: String,
    required: true,
    trim: true
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true,
    index: true
  }],
  isOrganic: {
    type: Boolean,
    default: false,
    index: true
  },
  inStock: {
    type: Boolean,
    default: true,
    index: true
  },
  stockQuantity: {
    type: Number,
    required: true,
    min: [0, 'Stock quantity cannot be negative'],
    default: 0
  },
  weight: {
    type: String,
    trim: true
  },
  volume: {
    type: String,
    trim: true
  },
  ingredients: multiLanguageArraySchema,
  benefits: multiLanguageArraySchema,
  usage: multiLanguageStringSchema,
  origin: {
    type: String,
    trim: true
  },
  certifications: [{
    type: String,
    trim: true
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  featured: {
    type: Boolean,
    default: false,
    index: true
  },
  seoTitle: multiLanguageStringSchema,
  seoDescription: multiLanguageStringSchema,
  slug: {
    type: multiLanguageStringSchema,
    required: true,
    unique: true,
    index: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
productSchema.index({ categoryId: 1, inStock: 1 });
productSchema.index({ featured: 1, isActive: 1 });
productSchema.index({ price: 1, categoryId: 1 });
productSchema.index({ tags: 1, categoryId: 1 });
productSchema.index({ 'title.ar': 'text', 'title.fr': 'text', 'title.en': 'text' });

// Virtual for checking if product is on sale
productSchema.virtual('isOnSale').get(function() {
  return this.originalPrice && this.originalPrice > this.price;
});

// Virtual for calculating discount amount
productSchema.virtual('discountAmount').get(function() {
  if (this.originalPrice && this.originalPrice > this.price) {
    return this.originalPrice - this.price;
  }
  return 0;
});

// Virtual for category relationship
productSchema.virtual('category', {
  ref: 'Category',
  localField: 'categoryId',
  foreignField: '_id',
  justOne: true
});

// Virtual for subcategory relationship
productSchema.virtual('subcategory', {
  ref: 'Category',
  localField: 'subcategoryId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to calculate discount percentage
productSchema.pre('save', function(next) {
  if (this.originalPrice && this.originalPrice > this.price) {
    this.discountPercentage = Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
  } else {
    this.discountPercentage = undefined;
  }
  
  // Update inStock based on stockQuantity
  this.inStock = this.stockQuantity > 0;
  
  next();
});

// Static method to find products by category
productSchema.statics.findByCategory = function(categoryId: string) {
  return this.find({ categoryId, isActive: true, inStock: true }).populate('categoryId');
};

// Static method to find featured products
productSchema.statics.findFeatured = function(limit: number = 10) {
  return this.find({ featured: true, isActive: true, inStock: true }).limit(limit);
};

// Static method to search products
productSchema.statics.searchProducts = function(query: string, categoryId?: string) {
  const searchQuery: any = {
    $text: { $search: query },
    isActive: true
  };

  if (categoryId) {
    searchQuery.categoryId = categoryId;
  }

  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .populate('categoryId')
    .sort({ score: { $meta: 'textScore' } });
};

// Instance method to update stock
productSchema.methods.updateStock = function(quantity: number, operation: 'add' | 'subtract' = 'subtract') {
  if (operation === 'add') {
    this.stockQuantity += quantity;
  } else {
    this.stockQuantity = Math.max(0, this.stockQuantity - quantity);
  }
  
  this.inStock = this.stockQuantity > 0;
  return this.save();
};

// Instance method to check if product can be ordered
productSchema.methods.canOrder = function(quantity: number = 1) {
  return this.isActive && this.inStock && this.stockQuantity >= quantity;
};

export const Product = mongoose.model<IProduct>('Product', productSchema);
