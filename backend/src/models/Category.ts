import mongoose, { Document, Schema } from 'mongoose';

// Interface pour les chaînes multilingues
interface IMultiLanguageString {
  ar: string;
  fr: string;
  en: string;
}

// Interface pour les données SEO multilingues
interface ISeoData {
  title: IMultiLanguageString;
  description: IMultiLanguageString;
  keywords: {
    ar: string[];
    fr: string[];
    en: string[];
  };
}

// Interface pour la catégorie
export interface ICategory extends Document {
  _id: mongoose.Types.ObjectId;
  name: IMultiLanguageString;
  slug: IMultiLanguageString;
  description: IMultiLanguageString;
  image?: string;
  parentCategory?: mongoose.Types.ObjectId;
  isActive: boolean;
  sortOrder: number;
  seo?: ISeoData;
  createdAt: Date;
  updatedAt: Date;
}

// Schéma pour les chaînes multilingues
const multiLanguageStringSchema = new Schema<IMultiLanguageString>({
  ar: { type: String, required: true, trim: true },
  fr: { type: String, required: true, trim: true },
  en: { type: String, required: true, trim: true }
}, { _id: false });

// Schéma pour les mots-clés multilingues
const multiLanguageKeywordsSchema = new Schema({
  ar: [{ type: String, trim: true }],
  fr: [{ type: String, trim: true }],
  en: [{ type: String, trim: true }]
}, { _id: false });

// Schéma SEO multilingue
const seoSchema = new Schema<ISeoData>({
  title: {
    type: multiLanguageStringSchema,
    required: [true, 'SEO title is required']
  },
  description: {
    type: multiLanguageStringSchema,
    required: [true, 'SEO description is required']
  },
  keywords: {
    type: multiLanguageKeywordsSchema,
    required: false
  }
}, { _id: false });

// Schéma de la catégorie
const categorySchema = new Schema<ICategory>({
  name: {
    type: multiLanguageStringSchema,
    required: [true, 'Category name is required'],
    index: true
  },
  slug: {
    type: multiLanguageStringSchema,
    required: [true, 'Category slug is required'],
    unique: true,
    index: true
  },
  description: {
    type: multiLanguageStringSchema,
    required: [true, 'Category description is required']
  },
  image: {
    type: String,
    trim: true,
    validate: {
      validator: function(v: string) {
        return !v || /^\/images\/categories\//.test(v);
      },
      message: 'Image path must start with /images/categories/'
    }
  },
  parentCategory: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  sortOrder: {
    type: Number,
    default: 0,
    min: [0, 'Sort order cannot be negative']
  },
  seo: {
    type: seoSchema,
    required: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index pour améliorer les performances
categorySchema.index({ slug: 1 });
categorySchema.index({ isActive: 1, sortOrder: 1 });
categorySchema.index({ parentCategory: 1 });

// Virtual pour obtenir les sous-catégories
categorySchema.virtual('subcategories', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parentCategory'
});

// Virtual pour obtenir les produits de cette catégorie
categorySchema.virtual('products', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'category'
});

// Méthode pour obtenir le chemin complet de la catégorie
categorySchema.methods.getFullPath = async function(language: 'ar' | 'fr' | 'en' = 'fr'): Promise<string> {
  if (!this.parentCategory) {
    return this.name[language];
  }

  const parent = await this.model('Category').findById(this.parentCategory);
  if (parent) {
    const parentPath = await parent.getFullPath(language);
    return `${parentPath} > ${this.name[language]}`;
  }

  return this.name[language];
};

// Méthode statique pour obtenir l'arbre des catégories
categorySchema.statics.getCategoryTree = async function(language: 'ar' | 'fr' | 'en' = 'fr') {
  const categories = await this.find({ isActive: true })
    .sort({ sortOrder: 1, [`name.${language}`]: 1 })
    .populate('subcategories');

  // Construire l'arbre hiérarchique
  const tree = categories.filter(cat => !cat.parentCategory);

  const buildTree = (parentId: mongoose.Types.ObjectId) => {
    return categories
      .filter(cat => cat.parentCategory && cat.parentCategory.equals(parentId))
      .map(cat => ({
        ...cat.toObject(),
        children: buildTree(cat._id)
      }));
  };

  return tree.map(cat => ({
    ...cat.toObject(),
    children: buildTree(cat._id)
  }));
};

// Middleware pre-save pour générer le slug automatiquement
categorySchema.pre('save', function(next) {
  if (this.isModified('name')) {
    // Générer les slugs pour chaque langue si ils n'existent pas
    if (!this.slug.ar) {
      this.slug.ar = this.name.ar
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }
    if (!this.slug.fr) {
      this.slug.fr = this.name.fr
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }
    if (!this.slug.en) {
      this.slug.en = this.name.en
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
    }
  }
  next();
});

// Middleware pre-remove pour vérifier les dépendances
categorySchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  try {
    // Vérifier s'il y a des produits dans cette catégorie
    const Product = mongoose.model('Product');
    const productCount = await Product.countDocuments({ category: this._id });
    
    if (productCount > 0) {
      throw new Error(`Cannot delete category: ${productCount} products are still assigned to this category`);
    }
    
    // Vérifier s'il y a des sous-catégories
    const subcategoryCount = await this.model('Category').countDocuments({ parentCategory: this._id });
    
    if (subcategoryCount > 0) {
      throw new Error(`Cannot delete category: ${subcategoryCount} subcategories exist under this category`);
    }
    
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Créer et exporter le modèle
export const Category = mongoose.model<ICategory>('Category', categorySchema);

export default Category;
