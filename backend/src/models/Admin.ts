import mongoose, { Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import { IAdmin } from '../types/index.js';

const adminSchema = new Schema<IAdmin>({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    lowercase: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [30, 'Username cannot exceed 30 characters'],
    match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'],
    index: true
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      'Please provide a valid email address'
    ],
    index: true
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters long'],
    select: false // Don't include password in queries by default
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  role: {
    type: String,
    required: [true, 'Role is required'],
    enum: ['super_admin', 'admin', 'manager', 'staff'],
    default: 'staff',
    index: true
  },
  permissions: [{
    type: String,
    enum: [
      'users.read', 'users.write', 'users.delete',
      'products.read', 'products.write', 'products.delete',
      'orders.read', 'orders.write', 'orders.delete',
      'customers.read', 'customers.write', 'customers.delete',
      'analytics.read', 'analytics.write',
      'settings.read', 'settings.write',
      'emails.read', 'emails.write',
      'inventory.read', 'inventory.write'
    ]
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  lastLogin: {
    type: Date,
    index: true
  },
  loginAttempts: {
    type: Number,
    default: 0,
    max: [5, 'Too many login attempts']
  },
  lockUntil: {
    type: Date,
    index: true
  },
  refreshTokens: [{
    type: String,
    select: false
  }]
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.refreshTokens;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

// Indexes for better query performance
adminSchema.index({ username: 1, isActive: 1 });
adminSchema.index({ email: 1, isActive: 1 });
adminSchema.index({ role: 1, isActive: 1 });
adminSchema.index({ lastLogin: -1 });

// Virtual for full name
adminSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for checking if account is locked
adminSchema.virtual('isLocked').get(function() {
  return !!(this.lockUntil && this.lockUntil > new Date());
});

// Virtual for role display name
adminSchema.virtual('roleDisplayName').get(function() {
  const roleNames: { [key: string]: string } = {
    'super_admin': 'Super Administrator',
    'admin': 'Administrator',
    'manager': 'Manager',
    'staff': 'Staff Member'
  };
  return roleNames[this.role] || this.role;
});

// Pre-save middleware to hash password
adminSchema.pre('save', async function(next) {
  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();
  
  try {
    // Hash password with cost of 12
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Pre-save middleware to set default permissions based on role
adminSchema.pre('save', function(next) {
  if (this.isModified('role') || this.isNew) {
    switch (this.role) {
      case 'super_admin':
        this.permissions = [
          'users.read', 'users.write', 'users.delete',
          'products.read', 'products.write', 'products.delete',
          'orders.read', 'orders.write', 'orders.delete',
          'customers.read', 'customers.write', 'customers.delete',
          'analytics.read', 'analytics.write',
          'settings.read', 'settings.write',
          'emails.read', 'emails.write',
          'inventory.read', 'inventory.write'
        ];
        break;
      case 'admin':
        this.permissions = [
          'users.read', 'users.write',
          'products.read', 'products.write', 'products.delete',
          'orders.read', 'orders.write', 'orders.delete',
          'customers.read', 'customers.write',
          'analytics.read',
          'emails.read', 'emails.write',
          'inventory.read', 'inventory.write'
        ];
        break;
      case 'manager':
        this.permissions = [
          'products.read', 'products.write',
          'orders.read', 'orders.write',
          'customers.read', 'customers.write',
          'analytics.read',
          'inventory.read', 'inventory.write'
        ];
        break;
      case 'staff':
        this.permissions = [
          'products.read',
          'orders.read', 'orders.write',
          'customers.read',
          'inventory.read'
        ];
        break;
    }
  }
  next();
});

// Instance method to compare password
adminSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  if (!this.password) return false;
  return bcrypt.compare(candidatePassword, this.password);
};

// Instance method to increment login attempts
adminSchema.methods.incLoginAttempts = function() {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    });
  }
  
  const updates: any = { $inc: { loginAttempts: 1 } };
  
  // Lock account after 5 attempts for 2 hours
  if (this.loginAttempts + 1 >= 5 && !this.isLocked) {
    updates.$set = { lockUntil: Date.now() + 2 * 60 * 60 * 1000 }; // 2 hours
  }
  
  return this.updateOne(updates);
};

// Instance method to reset login attempts
adminSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 }
  });
};

// Instance method to update last login
adminSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

// Instance method to check permission
adminSchema.methods.hasPermission = function(permission: string): boolean {
  return this.permissions.includes(permission);
};

// Instance method to add refresh token
adminSchema.methods.addRefreshToken = function(token: string) {
  this.refreshTokens.push(token);
  // Keep only the last 5 refresh tokens
  if (this.refreshTokens.length > 5) {
    this.refreshTokens = this.refreshTokens.slice(-5);
  }
  return this.save();
};

// Instance method to remove refresh token
adminSchema.methods.removeRefreshToken = function(token: string) {
  this.refreshTokens = this.refreshTokens.filter(t => t !== token);
  return this.save();
};

// Instance method to clear all refresh tokens
adminSchema.methods.clearRefreshTokens = function() {
  this.refreshTokens = [];
  return this.save();
};

// Static method to find by username or email
adminSchema.statics.findByUsernameOrEmail = function(identifier: string) {
  return this.findOne({
    $or: [
      { username: identifier.toLowerCase() },
      { email: identifier.toLowerCase() }
    ],
    isActive: true
  }).select('+password');
};

// Static method to find active admins by role
adminSchema.statics.findByRole = function(role: string) {
  return this.find({ role, isActive: true }).sort({ createdAt: -1 });
};

export const Admin = mongoose.model<IAdmin>('Admin', adminSchema);
