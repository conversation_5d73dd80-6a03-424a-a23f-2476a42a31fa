import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/environment.js';
import { Admin } from '../models/Admin.js';
import { ApiResponse } from '../types/index.js';

// Extend Request interface to include admin
declare global {
  namespace Express {
    interface Request {
      admin?: any;
    }
  }
}

interface JwtPayload {
  id: string;
  username: string;
  role: string;
  iat: number;
  exp: number;
}

// Middleware to verify JWT token
export const authenticateToken = async (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        message: 'Access token required'
      });
      return;
    }

    // Verify token
    const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
    
    // Find admin user
    const admin = await Admin.findById(decoded.id);
    
    if (!admin || !admin.isActive) {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired token'
      });
      return;
    }

    // Check if account is locked
    if (admin.isLocked) {
      res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to too many failed login attempts'
      });
      return;
    }

    // Attach admin to request
    req.admin = admin;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    } else if (error instanceof jwt.TokenExpiredError) {
      res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Authentication error'
      });
    }
  }
};

// Middleware to check permissions
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response<ApiResponse>, next: NextFunction): void => {
    if (!req.admin) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    if (!req.admin.hasPermission(permission)) {
      res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Middleware to check role
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response<ApiResponse>, next: NextFunction): void => {
    if (!req.admin) {
      res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
      return;
    }

    const allowedRoles = Array.isArray(roles) ? roles : [roles];
    
    if (!allowedRoles.includes(req.admin.role)) {
      res.status(403).json({
        success: false,
        message: 'Insufficient role privileges'
      });
      return;
    }

    next();
  };
};

// Middleware for optional authentication (for public endpoints that can benefit from auth)
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, config.jwt.secret) as JwtPayload;
      const admin = await Admin.findById(decoded.id);
      
      if (admin && admin.isActive && !admin.isLocked) {
        req.admin = admin;
      }
    }
    
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

// Utility function to generate JWT token
export const generateToken = (admin: any): string => {
  return jwt.sign(
    {
      id: admin._id,
      username: admin.username,
      role: admin.role
    },
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  );
};

// Utility function to generate refresh token
export const generateRefreshToken = (admin: any): string => {
  return jwt.sign(
    {
      id: admin._id,
      username: admin.username,
      type: 'refresh'
    },
    config.jwt.refreshSecret,
    { expiresIn: config.jwt.refreshExpiresIn }
  );
};

// Utility function to verify refresh token
export const verifyRefreshToken = (token: string): JwtPayload => {
  return jwt.verify(token, config.jwt.refreshSecret) as JwtPayload;
};
