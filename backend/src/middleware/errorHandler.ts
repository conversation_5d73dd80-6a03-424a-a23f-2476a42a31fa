import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types/index.js';
import { config } from '../config/environment.js';

// Custom error class
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Error handling middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal server error';
  let errorDetails: string | undefined;

  // Handle different types of errors
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
  } else if (error.name === 'ValidationError') {
    // Mongoose validation error
    statusCode = 400;
    message = 'Validation error';
    errorDetails = Object.values((error as any).errors)
      .map((err: any) => err.message)
      .join(', ');
  } else if (error.name === 'CastError') {
    // Mongoose cast error (invalid ObjectId)
    statusCode = 400;
    message = 'Invalid ID format';
  } else if ((error as any).code === 11000) {
    // Mongoose duplicate key error
    statusCode = 409;
    message = 'Duplicate entry';
    const field = Object.keys((error as any).keyValue)[0];
    errorDetails = `${field} already exists`;
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
  } else if (error.name === 'MongoNetworkError') {
    statusCode = 503;
    message = 'Database connection error';
  } else if (error.name === 'MongoTimeoutError') {
    statusCode = 504;
    message = 'Database timeout error';
  }

  // Log error in development
  if (config.server.env === 'development') {
    console.error('Error:', {
      message: error.message,
      stack: error.stack,
      statusCode,
      url: req.url,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query
    });
  }

  // Send error response
  const response: ApiResponse = {
    success: false,
    message,
    error: errorDetails || (config.server.env === 'development' ? error.message : undefined)
  };

  res.status(statusCode).json(response);
};

// 404 handler
export const notFoundHandler = (
  req: Request,
  res: Response<ApiResponse>,
  next: NextFunction
): void => {
  const error = new AppError(`Route ${req.originalUrl} not found`, 404);
  next(error);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Rate limiting error handler
export const rateLimitHandler = (
  req: Request,
  res: Response<ApiResponse>
): void => {
  res.status(429).json({
    success: false,
    message: 'Too many requests, please try again later'
  });
};

// CORS error handler
export const corsErrorHandler = (
  req: Request,
  res: Response<ApiResponse>
): void => {
  res.status(403).json({
    success: false,
    message: 'CORS policy violation'
  });
};

// Validation error formatter
export const formatValidationError = (error: any): string => {
  if (error.details) {
    return error.details.map((detail: any) => detail.message).join(', ');
  }
  return error.message || 'Validation error';
};

// Database error formatter
export const formatDatabaseError = (error: any): { statusCode: number; message: string; details?: string } => {
  if (error.name === 'ValidationError') {
    return {
      statusCode: 400,
      message: 'Validation error',
      details: Object.values(error.errors).map((err: any) => err.message).join(', ')
    };
  }

  if (error.name === 'CastError') {
    return {
      statusCode: 400,
      message: 'Invalid ID format',
      details: `Invalid ${error.path}: ${error.value}`
    };
  }

  if (error.code === 11000) {
    const field = Object.keys(error.keyValue)[0];
    return {
      statusCode: 409,
      message: 'Duplicate entry',
      details: `${field} already exists`
    };
  }

  if (error.name === 'MongoNetworkError') {
    return {
      statusCode: 503,
      message: 'Database connection error'
    };
  }

  if (error.name === 'MongoTimeoutError') {
    return {
      statusCode: 504,
      message: 'Database timeout error'
    };
  }

  return {
    statusCode: 500,
    message: 'Database error'
  };
};

// Success response helper
export const sendSuccess = (
  res: Response<ApiResponse>,
  data?: any,
  message: string = 'Success',
  statusCode: number = 200,
  pagination?: any
): void => {
  const response: ApiResponse = {
    success: true,
    message,
    data,
    pagination
  };

  res.status(statusCode).json(response);
};

// Error response helper
export const sendError = (
  res: Response<ApiResponse>,
  message: string,
  statusCode: number = 500,
  error?: string
): void => {
  const response: ApiResponse = {
    success: false,
    message,
    error
  };

  res.status(statusCode).json(response);
};
