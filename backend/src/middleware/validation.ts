import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { ApiResponse } from '../types/index.js';

// Generic validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response<ApiResponse>, next: NextFunction): void => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      res.status(400).json({
        success: false,
        message: 'Validation error',
        error: errorMessages.join(', ')
      });
      return;
    }
    
    next();
  };
};

// Query validation middleware
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response<ApiResponse>, next: NextFunction): void => {
    const { error } = schema.validate(req.query, { abortEarly: false });
    
    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      res.status(400).json({
        success: false,
        message: 'Query validation error',
        error: errorMessages.join(', ')
      });
      return;
    }
    
    next();
  };
};

// Params validation middleware
export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response<ApiResponse>, next: NextFunction): void => {
    const { error } = schema.validate(req.params, { abortEarly: false });
    
    if (error) {
      const errorMessages = error.details.map(detail => detail.message);
      res.status(400).json({
        success: false,
        message: 'Parameter validation error',
        error: errorMessages.join(', ')
      });
      return;
    }
    
    next();
  };
};

// Common validation schemas
export const schemas = {
  // MongoDB ObjectId validation
  objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
  
  // Pagination schemas
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().default('createdAt'),
    order: Joi.string().valid('asc', 'desc').default('desc')
  }),

  // Product schemas
  createProduct: Joi.object({
    title: Joi.object({
      ar: Joi.string().required(),
      fr: Joi.string().required(),
      en: Joi.string().required()
    }).required(),
    description: Joi.object({
      ar: Joi.string().required(),
      fr: Joi.string().required(),
      en: Joi.string().required()
    }).required(),
    price: Joi.number().positive().required(),
    originalPrice: Joi.number().positive().optional(),
    category: Joi.string().valid('argan', 'honey', 'amlou').required(),
    subcategory: Joi.string().optional(),
    images: Joi.array().items(Joi.string().uri()).min(1).required(),
    tags: Joi.array().items(Joi.string()).default([]),
    isOrganic: Joi.boolean().default(false),
    stockQuantity: Joi.number().integer().min(0).required(),
    weight: Joi.string().optional(),
    volume: Joi.string().optional(),
    ingredients: Joi.object({
      ar: Joi.array().items(Joi.string()),
      fr: Joi.array().items(Joi.string()),
      en: Joi.array().items(Joi.string())
    }).optional(),
    benefits: Joi.object({
      ar: Joi.array().items(Joi.string()),
      fr: Joi.array().items(Joi.string()),
      en: Joi.array().items(Joi.string())
    }).optional(),
    usage: Joi.object({
      ar: Joi.string(),
      fr: Joi.string(),
      en: Joi.string()
    }).optional(),
    origin: Joi.string().optional(),
    certifications: Joi.array().items(Joi.string()).default([]),
    featured: Joi.boolean().default(false),
    slug: Joi.object({
      ar: Joi.string().required(),
      fr: Joi.string().required(),
      en: Joi.string().required()
    }).required()
  }),

  updateProduct: Joi.object({
    title: Joi.object({
      ar: Joi.string(),
      fr: Joi.string(),
      en: Joi.string()
    }).optional(),
    description: Joi.object({
      ar: Joi.string(),
      fr: Joi.string(),
      en: Joi.string()
    }).optional(),
    price: Joi.number().positive().optional(),
    originalPrice: Joi.number().positive().optional(),
    category: Joi.string().valid('argan', 'honey', 'amlou').optional(),
    subcategory: Joi.string().optional(),
    images: Joi.array().items(Joi.string().uri()).min(1).optional(),
    tags: Joi.array().items(Joi.string()).optional(),
    isOrganic: Joi.boolean().optional(),
    stockQuantity: Joi.number().integer().min(0).optional(),
    weight: Joi.string().optional(),
    volume: Joi.string().optional(),
    ingredients: Joi.object({
      ar: Joi.array().items(Joi.string()),
      fr: Joi.array().items(Joi.string()),
      en: Joi.array().items(Joi.string())
    }).optional(),
    benefits: Joi.object({
      ar: Joi.array().items(Joi.string()),
      fr: Joi.array().items(Joi.string()),
      en: Joi.array().items(Joi.string())
    }).optional(),
    usage: Joi.object({
      ar: Joi.string(),
      fr: Joi.string(),
      en: Joi.string()
    }).optional(),
    origin: Joi.string().optional(),
    certifications: Joi.array().items(Joi.string()).optional(),
    featured: Joi.boolean().optional(),
    isActive: Joi.boolean().optional(),
    slug: Joi.object({
      ar: Joi.string(),
      fr: Joi.string(),
      en: Joi.string()
    }).optional()
  }),

  // Customer schemas
  createCustomer: Joi.object({
    firstName: Joi.string().max(50).required(),
    lastName: Joi.string().max(50).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^(\+212|0)[5-7][0-9]{8}$/).required(),
    age: Joi.string().valid('18-25', '26-35', '36-45', '46-55', '56-65', '65+').optional(),
    dateOfBirth: Joi.date().optional(),
    preferredLanguage: Joi.string().valid('ar', 'fr', 'en').default('ar')
  }),

  updateCustomer: Joi.object({
    firstName: Joi.string().max(50).optional(),
    lastName: Joi.string().max(50).optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().pattern(/^(\+212|0)[5-7][0-9]{8}$/).optional(),
    age: Joi.string().valid('18-25', '26-35', '36-45', '46-55', '56-65', '65+').optional(),
    dateOfBirth: Joi.date().optional(),
    preferredLanguage: Joi.string().valid('ar', 'fr', 'en').optional(),
    isActive: Joi.boolean().optional(),
    notes: Joi.string().max(500).optional()
  }),

  // Order schemas
  createOrder: Joi.object({
    customerId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    customerInfo: Joi.object({
      firstName: Joi.string().required(),
      lastName: Joi.string().required(),
      email: Joi.string().email().required(),
      phone: Joi.string().required()
    }).required(),
    shippingAddress: Joi.object({
      streetAddress: Joi.string().required(),
      city: Joi.string().required(),
      postalCode: Joi.string().pattern(/^\d{5}$/).required(),
      country: Joi.string().default('Morocco'),
      region: Joi.string().required()
    }).required(),
    items: Joi.array().items(
      Joi.object({
        productId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
        productTitle: Joi.string().required(),
        productImage: Joi.string().required(),
        productCategory: Joi.string().valid('argan', 'honey', 'amlou').required(),
        price: Joi.number().positive().required(),
        originalPrice: Joi.number().positive().optional(),
        quantity: Joi.number().integer().min(1).required(),
        total: Joi.number().positive().required()
      })
    ).min(1).required(),
    paymentMethod: Joi.string().valid('cash_on_delivery', 'credit_card', 'bank_transfer', 'paypal').required(),
    shippingFee: Joi.number().min(0).default(0),
    notes: Joi.string().max(500).optional()
  }),

  updateOrderStatus: Joi.object({
    orderStatus: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded').required(),
    adminNotes: Joi.string().max(500).optional()
  }),

  updatePaymentStatus: Joi.object({
    paymentStatus: Joi.string().valid('pending', 'paid', 'failed', 'refunded', 'partially_refunded').required()
  }),

  addTracking: Joi.object({
    trackingNumber: Joi.string().required()
  }),

  // Admin schemas
  createAdmin: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    firstName: Joi.string().max(50).required(),
    lastName: Joi.string().max(50).required(),
    role: Joi.string().valid('super_admin', 'admin', 'manager', 'staff').default('staff')
  }),

  updateAdmin: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).optional(),
    email: Joi.string().email().optional(),
    password: Joi.string().min(8).optional(),
    firstName: Joi.string().max(50).optional(),
    lastName: Joi.string().max(50).optional(),
    role: Joi.string().valid('super_admin', 'admin', 'manager', 'staff').optional(),
    isActive: Joi.boolean().optional()
  }),

  login: Joi.object({
    username: Joi.string().required(),
    password: Joi.string().required()
  }),

  refreshToken: Joi.object({
    refreshToken: Joi.string().required()
  })
};
