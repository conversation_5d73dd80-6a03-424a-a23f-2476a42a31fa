import { Document, Types } from 'mongoose';

// Base interface for all documents
export interface BaseDocument extends Document {
  _id: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

// Product related types
export interface IProduct extends BaseDocument {
  title: {
    ar: string;
    fr: string;
    en: string;
  };
  description: {
    ar: string;
    fr: string;
    en: string;
  };
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  categoryId: Types.ObjectId;
  subcategoryId?: Types.ObjectId;
  images: string[];
  tags: string[];
  isOrganic: boolean;
  inStock: boolean;
  stockQuantity: number;
  weight?: string;
  volume?: string;
  ingredients?: {
    ar: string[];
    fr: string[];
    en: string[];
  };
  benefits?: {
    ar: string[];
    fr: string[];
    en: string[];
  };
  usage?: {
    ar: string;
    fr: string;
    en: string;
  };
  origin?: string;
  certifications?: string[];
  isActive: boolean;
  featured: boolean;
  seoTitle?: {
    ar: string;
    fr: string;
    en: string;
  };
  seoDescription?: {
    ar: string;
    fr: string;
    en: string;
  };
  slug: {
    ar: string;
    fr: string;
    en: string;
  };
}

// Customer related types
export interface ICustomer extends BaseDocument {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  age?: string;
  dateOfBirth?: Date;
  isActive: boolean;
  emailVerified: boolean;
  phoneVerified: boolean;
  preferredLanguage: 'ar' | 'fr' | 'en';
  addresses: Types.ObjectId[];
  orders: Types.ObjectId[];
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: Date;
  notes?: string;
}

// Address related types
export interface IAddress extends BaseDocument {
  customerId: Types.ObjectId;
  type: 'shipping' | 'billing' | 'both';
  firstName: string;
  lastName: string;
  streetAddress: string;
  city: string;
  postalCode: string;
  country: string;
  region: string;
  isDefault: boolean;
  isActive: boolean;
}

// Order related types
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded';
export type PaymentMethod = 'cash_on_delivery' | 'credit_card' | 'bank_transfer' | 'paypal';

export interface IOrderItem {
  productId: Types.ObjectId;
  productTitle: string;
  productImage: string;
  productCategory: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  total: number;
}

export interface IOrder extends BaseDocument {
  orderId: string;
  customerId: Types.ObjectId;
  customerInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  shippingAddress: {
    streetAddress: string;
    city: string;
    postalCode: string;
    country: string;
    region: string;
  };
  billingAddress?: {
    streetAddress: string;
    city: string;
    postalCode: string;
    country: string;
    region: string;
  };
  items: IOrderItem[];
  subtotal: number;
  shippingFee: number;
  tax?: number;
  discount?: number;
  total: number;
  paymentMethod: PaymentMethod;
  paymentStatus: PaymentStatus;
  orderStatus: OrderStatus;
  orderDate: Date;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  trackingNumber?: string;
  notes?: string;
  adminNotes?: string;
  emailSent: boolean;
  invoiceSent: boolean;
}

// Admin user types
export interface IAdmin extends BaseDocument {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  role: 'super_admin' | 'admin' | 'manager' | 'staff';
  permissions: string[];
  isActive: boolean;
  lastLogin?: Date;
  loginAttempts: number;
  lockUntil?: Date;
  refreshTokens: string[];
}

// Inventory types
export interface IInventoryLog extends BaseDocument {
  productId: Types.ObjectId;
  type: 'stock_in' | 'stock_out' | 'adjustment' | 'damaged' | 'expired';
  quantity: number;
  previousStock: number;
  newStock: number;
  reason: string;
  orderId?: Types.ObjectId;
  adminId: Types.ObjectId;
  notes?: string;
}

// Email template types
export interface IEmailTemplate extends BaseDocument {
  name: string;
  subject: {
    ar: string;
    fr: string;
    en: string;
  };
  htmlContent: {
    ar: string;
    fr: string;
    en: string;
  };
  textContent: {
    ar: string;
    fr: string;
    en: string;
  };
  type: 'order_confirmation' | 'order_shipped' | 'order_delivered' | 'password_reset' | 'welcome';
  isActive: boolean;
  variables: string[];
}

// Analytics types
export interface ISalesAnalytics extends BaseDocument {
  date: Date;
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  newCustomers: number;
  topProducts: {
    productId: Types.ObjectId;
    productTitle: string;
    quantity: number;
    revenue: number;
  }[];
  ordersByStatus: {
    pending: number;
    confirmed: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
  };
  averageOrderValue: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Request types
export interface PaginationQuery {
  page?: string;
  limit?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface ProductQuery extends PaginationQuery {
  category?: string;
  inStock?: string;
  featured?: string;
  search?: string;
  minPrice?: string;
  maxPrice?: string;
  tags?: string;
}

export interface OrderQuery extends PaginationQuery {
  status?: string;
  paymentStatus?: string;
  customerId?: string;
  startDate?: string;
  endDate?: string;
}

export interface CustomerQuery extends PaginationQuery {
  search?: string;
  isActive?: string;
  emailVerified?: string;
}
