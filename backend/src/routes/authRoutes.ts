import { Router } from 'express';
import {
  login,
  logout,
  logoutAll,
  refreshToken,
  getProfile,
  updateProfile,
  changePassword,
  createAdmin,
  getAdmins,
  updateAdmin,
  deleteAdmin
} from '../controllers/authController.js';
import { authenticateToken, requireRole, requirePermission } from '../middleware/auth.js';
import { validate, validateQuery, validateParams, schemas } from '../middleware/validation.js';
import Joi from 'joi';

const router = Router();

// Public authentication routes
router.post('/login', validate(schemas.login), login);
router.post('/refresh', validate(schemas.refreshToken), refreshToken);

// Protected routes (authentication required)
router.use(authenticateToken);

// Profile management
router.get('/profile', getProfile);
router.put(
  '/profile',
  validate(Joi.object({
    firstName: Joi.string().max(50).optional(),
    lastName: Joi.string().max(50).optional(),
    email: Joi.string().email().optional()
  })),
  updateProfile
);

router.post(
  '/change-password',
  validate(Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(8).required()
  })),
  changePassword
);

// Logout
router.post('/logout', validate(Joi.object({
  refreshToken: Joi.string().optional()
})), logout);

router.post('/logout-all', logoutAll);

// Admin management (Super Admin and Admin only)
router.get(
  '/admins',
  requirePermission('users.read'),
  validateQuery(schemas.pagination),
  getAdmins
);

router.post(
  '/admins',
  requireRole(['super_admin']),
  validate(schemas.createAdmin),
  createAdmin
);

router.put(
  '/admins/:id',
  requireRole(['super_admin']),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.updateAdmin),
  updateAdmin
);

router.delete(
  '/admins/:id',
  requireRole(['super_admin']),
  validateParams(Joi.object({ id: schemas.objectId })),
  deleteAdmin
);

export default router;
