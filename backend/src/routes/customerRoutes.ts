import { Router } from 'express';
import {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerAddresses,
  addCustomerAddress,
  updateCustomerAddress,
  deleteCustomerAddress,
  getCustomerOrders,
  getCustomerStats,
  searchCustomers,
  getCustomerAnalytics
} from '../controllers/customerController.js';
import { authenticateToken, requirePermission, optionalAuth } from '../middleware/auth.js';
import { validate, validateQuery, validateParams, schemas } from '../middleware/validation.js';
import Joi from 'joi';

const router = Router();

// Public routes
router.post('/', validate(schemas.createCustomer), createCustomer);

// Admin routes (authentication required)
router.use(authenticateToken);

// Customer management
router.get(
  '/',
  requirePermission('customers.read'),
  validateQuery(schemas.pagination),
  getCustomers
);

router.get(
  '/search',
  requirePermission('customers.read'),
  searchCustomers
);

router.get(
  '/analytics',
  requirePermission('analytics.read'),
  getCustomerAnalytics
);

router.get(
  '/:id',
  requirePermission('customers.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  getCustomerById
);

router.put(
  '/:id',
  requirePermission('customers.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.updateCustomer),
  updateCustomer
);

router.delete(
  '/:id',
  requirePermission('customers.delete'),
  validateParams(Joi.object({ id: schemas.objectId })),
  deleteCustomer
);

// Customer addresses
router.get(
  '/:id/addresses',
  requirePermission('customers.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  getCustomerAddresses
);

router.post(
  '/:id/addresses',
  requirePermission('customers.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(Joi.object({
    type: Joi.string().valid('shipping', 'billing', 'both').default('shipping'),
    streetAddress: Joi.string().required(),
    city: Joi.string().required(),
    postalCode: Joi.string().pattern(/^\d{5}$/).required(),
    country: Joi.string().default('Morocco'),
    region: Joi.string().required(),
    isDefault: Joi.boolean().default(false)
  })),
  addCustomerAddress
);

router.put(
  '/:id/addresses/:addressId',
  requirePermission('customers.write'),
  validateParams(Joi.object({ 
    id: schemas.objectId,
    addressId: schemas.objectId
  })),
  validate(Joi.object({
    type: Joi.string().valid('shipping', 'billing', 'both').optional(),
    streetAddress: Joi.string().optional(),
    city: Joi.string().optional(),
    postalCode: Joi.string().pattern(/^\d{5}$/).optional(),
    country: Joi.string().optional(),
    region: Joi.string().optional(),
    isDefault: Joi.boolean().optional()
  })),
  updateCustomerAddress
);

router.delete(
  '/:id/addresses/:addressId',
  requirePermission('customers.write'),
  validateParams(Joi.object({ 
    id: schemas.objectId,
    addressId: schemas.objectId
  })),
  deleteCustomerAddress
);

// Customer orders
router.get(
  '/:id/orders',
  requirePermission('customers.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validateQuery(schemas.pagination),
  getCustomerOrders
);

// Customer statistics
router.get(
  '/:id/stats',
  requirePermission('customers.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  getCustomerStats
);

export default router;
