import express from 'express';
import {
  getAllCategories,
  getCategoryById,
  getCategoryTree,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryProducts
} from '../controllers/categoryController.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';
import { body, param, validationResult } from 'express-validator';

// Validation middleware
const validateRequest = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: errors.array()
    });
  }
  next();
};

const router = express.Router();

// Validation schemas
const createCategoryValidation = [
  body('name.ar').notEmpty().withMessage('Arabic name is required'),
  body('name.fr').notEmpty().withMessage('French name is required'),
  body('name.en').notEmpty().withMessage('English name is required'),
  body('description.ar').notEmpty().withMessage('Arabic description is required'),
  body('description.fr').notEmpty().withMessage('French description is required'),
  body('description.en').notEmpty().withMessage('English description is required'),
  body('image').optional().isString().withMessage('Image must be a string'),
  body('parentCategory').optional().isMongoId().withMessage('Parent category must be a valid ObjectId'),
  body('sortOrder').optional().isInt({ min: 0 }).withMessage('Sort order must be a positive integer'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
];

const updateCategoryValidation = [
  param('id').isMongoId().withMessage('Invalid category ID'),
  body('name.ar').optional().notEmpty().withMessage('Arabic name cannot be empty'),
  body('name.fr').optional().notEmpty().withMessage('French name cannot be empty'),
  body('name.en').optional().notEmpty().withMessage('English name cannot be empty'),
  body('description.ar').optional().notEmpty().withMessage('Arabic description cannot be empty'),
  body('description.fr').optional().notEmpty().withMessage('French description cannot be empty'),
  body('description.en').optional().notEmpty().withMessage('English description cannot be empty'),
  body('image').optional().isString().withMessage('Image must be a string'),
  body('parentCategory').optional().isMongoId().withMessage('Parent category must be a valid ObjectId'),
  body('sortOrder').optional().isInt({ min: 0 }).withMessage('Sort order must be a positive integer'),
  body('isActive').optional().isBoolean().withMessage('isActive must be a boolean')
];

const categoryIdValidation = [
  param('id').isMongoId().withMessage('Invalid category ID')
];

// Public routes
router.get('/', getAllCategories);
router.get('/tree', getCategoryTree);
router.get('/:id', categoryIdValidation, validateRequest, getCategoryById);
router.get('/:id/products', categoryIdValidation, validateRequest, getCategoryProducts);

// Admin routes
router.post('/', 
  authenticateToken, 
  requirePermission('manage_categories'),
  createCategoryValidation,
  validateRequest,
  createCategory
);

router.put('/:id',
  authenticateToken,
  requirePermission('manage_categories'),
  updateCategoryValidation,
  validateRequest,
  updateCategory
);

router.delete('/:id',
  authenticateToken,
  requirePermission('manage_categories'),
  categoryIdValidation,
  validateRequest,
  deleteCategory
);

export default router;
