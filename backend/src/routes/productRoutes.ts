import { Router } from 'express';
import {
  getProducts,
  getProductById,
  getProductBySlug,
  getProductsByCategory,
  getFeaturedProducts,
  searchProducts,
  createProduct,
  updateProduct,
  deleteProduct,
  updateProductStock,
  getProductAnalytics
} from '../controllers/productController.js';
import { authenticateToken, requirePermission, optionalAuth } from '../middleware/auth.js';
import { validate, validateQuery, validateParams, schemas } from '../middleware/validation.js';
import Joi from 'joi';

const router = Router();

// Public routes (no authentication required)
router.get('/', validateQuery(schemas.pagination), getProducts);
router.get('/featured', getFeaturedProducts);
router.get('/search', searchProducts);
router.get('/category/:category', validateQuery(schemas.pagination), getProductsByCategory);
router.get('/slug/:slug/:lang?', getProductBySlug);
router.get('/:id', validateParams(Joi.object({ id: schemas.objectId })), getProductById);

// Admin routes (authentication required)
router.use(authenticateToken);

// Product management routes
router.post(
  '/',
  requirePermission('products.write'),
  validate(schemas.createProduct),
  createProduct
);

router.put(
  '/:id',
  requirePermission('products.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.updateProduct),
  updateProduct
);

router.delete(
  '/:id',
  requirePermission('products.delete'),
  validateParams(Joi.object({ id: schemas.objectId })),
  deleteProduct
);

// Stock management
router.patch(
  '/:id/stock',
  requirePermission('inventory.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(Joi.object({
    quantity: Joi.number().integer().min(0).required(),
    operation: Joi.string().valid('set', 'add', 'subtract').default('set')
  })),
  updateProductStock
);

// Analytics
router.get(
  '/analytics/overview',
  requirePermission('analytics.read'),
  getProductAnalytics
);

export default router;
