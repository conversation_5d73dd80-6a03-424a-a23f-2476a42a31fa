import { Router } from 'express';
import {
  getOrders,
  getOrderById,
  getOrderByOrderId,
  createOrder,
  updateOrderStatus,
  updatePaymentStatus,
  addTrackingNumber,
  cancelOrder,
  getOrdersByStatus,
  getOrdersByDateRange,
  getOrderAnalytics,
  calculateRefund
} from '../controllers/orderController.js';
import { authenticateToken, requirePermission, optionalAuth } from '../middleware/auth.js';
import { validate, validateQuery, validateParams, schemas } from '../middleware/validation.js';
import Joi from 'joi';

const router = Router();

// Public routes
router.post('/', validate(schemas.createOrder), createOrder);
router.get('/order/:orderId', getOrderByOrderId);

// Admin routes (authentication required)
router.use(authenticateToken);

// Order management
router.get(
  '/',
  requirePermission('orders.read'),
  validateQuery(schemas.pagination),
  getOrders
);

router.get(
  '/analytics',
  requirePermission('analytics.read'),
  getOrderAnalytics
);

router.get(
  '/status/:status',
  requirePermission('orders.read'),
  validateParams(Joi.object({ 
    status: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded').required()
  })),
  getOrdersByStatus
);

router.get(
  '/date-range',
  requirePermission('orders.read'),
  validateQuery(Joi.object({
    startDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().required()
  })),
  getOrdersByDateRange
);

router.get(
  '/:id',
  requirePermission('orders.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  getOrderById
);

// Order updates
router.patch(
  '/:id/status',
  requirePermission('orders.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.updateOrderStatus),
  updateOrderStatus
);

router.patch(
  '/:id/payment-status',
  requirePermission('orders.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.updatePaymentStatus),
  updatePaymentStatus
);

router.patch(
  '/:id/tracking',
  requirePermission('orders.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(schemas.addTracking),
  addTrackingNumber
);

router.patch(
  '/:id/cancel',
  requirePermission('orders.write'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validate(Joi.object({
    reason: Joi.string().max(500).optional()
  })),
  cancelOrder
);

// Refund calculation
router.get(
  '/:id/refund',
  requirePermission('orders.read'),
  validateParams(Joi.object({ id: schemas.objectId })),
  validateQuery(Joi.object({
    refundShipping: Joi.boolean().default(false)
  })),
  calculateRefund
);

export default router;
