#!/usr/bin/env tsx

/**
 * Script de peuplement de la base de données MongoDB Atlas
 * Usage: npm run seed
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import { Product } from '../models/Product.js';
import { Category } from '../models/Category.js';
import { Customer } from '../models/Customer.js';
import { Order } from '../models/Order.js';

// Charger les variables d'environnement
config();

// Générer des ObjectIds valides
const categoryIds = {
  amlou: new mongoose.Types.ObjectId(),
  honey: new mongoose.Types.ObjectId(),
  arganOil: new mongoose.Types.ObjectId()
};

// Données des catégories
const categoriesData = [
  {
    _id: categoryIds.amlou,
    name: 'Amlou',
    slug: 'amlou',
    description: 'Pâte traditionnelle berbère à base d\'amandes, d\'huile d\'argan et de miel',
    image: '/images/categories/amlou-category.jpg',
    isActive: true,
    sortOrder: 1,
    seo: {
      title: 'Amlou Traditionnel Bio - Pâte d\'Amandes et Huile d\'Argan',
      description: 'Découvrez notre amlou artisanal, préparé selon la tradition berbère avec des amandes grillées, de l\'huile d\'argan pure et du miel naturel.',
      keywords: ['amlou', 'amandes', 'huile d\'argan', 'miel', 'bio', 'traditionnel']
    }
  },
  {
    _id: categoryIds.honey,
    name: 'Miel',
    slug: 'honey',
    description: 'Miel pur et naturel récolté dans les montagnes du Maroc',
    image: '/images/categories/honey-category.jpg',
    isActive: true,
    sortOrder: 2,
    seo: {
      title: 'Miel Naturel du Maroc - Miel de Montagne et d\'Eucalyptus',
      description: 'Miel pur récolté dans les montagnes de l\'Atlas, aux propriétés thérapeutiques exceptionnelles.',
      keywords: ['miel', 'montagne', 'eucalyptus', 'naturel', 'thérapeutique', 'atlas']
    }
  },
  {
    _id: categoryIds.arganOil,
    name: 'Huile d\'Argan',
    slug: 'argan-oil',
    description: 'Huile d\'argan pure, pressée à froid, pour cuisine et cosmétique',
    image: '/images/categories/argan-oil-category.jpg',
    isActive: true,
    sortOrder: 3,
    seo: {
      title: 'Huile d\'Argan Pure Bio - Pressée à Froid',
      description: 'Huile d\'argan 100% pure, pressée à froid, idéale pour la cuisine et les soins cosmétiques.',
      keywords: ['huile d\'argan', 'pressée à froid', 'bio', 'cosmétique', 'cuisine', 'essaouira']
    }
  }
];

// Données des produits
const productsData = [
  {
    _id: new mongoose.Types.ObjectId(),
    name: 'Amlou Traditionnel',
    slug: {
      fr: 'amlou-traditionnel',
      en: 'traditional-amlou',
      ar: 'اللوز-التقليدي'
    },
    description: 'Amlou artisanal préparé selon la tradition berbère avec des amandes grillées, de l\'huile d\'argan pure et du miel naturel.',
    shortDescription: 'Amlou traditionnel aux amandes grillées et huile d\'argan',
    price: 120,
    originalPrice: 150,
    category: categoryIds.amlou,
    images: [
      '/images/products/amlou-traditionnel-1.jpg',
      '/images/products/amlou-traditionnel-2.jpg',
      '/images/products/amlou-traditionnel-3.jpg'
    ],
    inStock: true,
    stockQuantity: 50,
    featured: true,
    tags: ['bio', 'traditionnel', 'artisanal', 'amandes', 'argan'],
    nutritionalInfo: {
      calories: 580,
      protein: 18,
      carbs: 25,
      fat: 48,
      fiber: 12,
      sugar: 20,
      sodium: 5
    },
    ingredients: ['Amandes grillées (60%)', 'Huile d\'argan pure (25%)', 'Miel naturel (15%)'],
    weight: '250g',
    dimensions: {
      length: 8,
      width: 8,
      height: 6
    },
    origin: 'Essaouira, Maroc',
    certifications: ['Bio', 'Commerce Équitable'],
    storageInstructions: 'Conserver dans un endroit frais et sec, à l\'abri de la lumière',
    shelfLife: '12 mois',
    isActive: true,
    seo: {
      title: 'Amlou Traditionnel Bio - 250g | Amandes, Argan, Miel',
      description: 'Amlou artisanal préparé selon la tradition berbère. Amandes grillées, huile d\'argan pure et miel naturel. Livraison gratuite.',
      keywords: ['amlou traditionnel', 'amandes grillées', 'huile d\'argan', 'miel naturel', 'bio']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('6507f1f77bcf86cd799439012'),
    name: 'Miel de Montagne',
    slug: {
      fr: 'miel-de-montagne',
      en: 'mountain-honey',
      ar: 'عسل-الجبل'
    },
    description: 'Miel pur récolté dans les montagnes de l\'Atlas, aux propriétés thérapeutiques exceptionnelles.',
    shortDescription: 'Miel pur des montagnes de l\'Atlas',
    price: 200,
    originalPrice: 250,
    category: new mongoose.Types.ObjectId('650000000000000000000002'),
    images: [
      '/images/products/miel-montagne-1.jpg',
      '/images/products/miel-montagne-2.jpg',
      '/images/products/miel-montagne-3.jpg'
    ],
    inStock: true,
    stockQuantity: 30,
    featured: true,
    tags: ['bio', 'montagne', 'thérapeutique', 'atlas', 'pur'],
    nutritionalInfo: {
      calories: 304,
      protein: 0.3,
      carbs: 82,
      fat: 0,
      fiber: 0.2,
      sugar: 82,
      sodium: 4
    },
    ingredients: ['Miel pur de montagne (100%)'],
    weight: '500g',
    dimensions: {
      length: 10,
      width: 10,
      height: 12
    },
    origin: 'Montagnes de l\'Atlas, Maroc',
    certifications: ['Bio', 'Récolte Traditionnelle'],
    storageInstructions: 'Conserver à température ambiante, à l\'abri de l\'humidité',
    shelfLife: '24 mois',
    isActive: true,
    seo: {
      title: 'Miel de Montagne Bio - 500g | Atlas Maroc',
      description: 'Miel pur récolté dans les montagnes de l\'Atlas. Propriétés thérapeutiques exceptionnelles. Qualité premium.',
      keywords: ['miel de montagne', 'atlas', 'thérapeutique', 'bio', 'maroc']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('6507f1f77bcf86cd799439013'),
    name: 'Huile d\'Argan Pure',
    slug: {
      fr: 'huile-argan-pure',
      en: 'pure-argan-oil',
      ar: 'زيت-الأركان-الخالص'
    },
    description: 'Huile d\'argan 100% pure, pressée à froid, idéale pour la cuisine et les soins cosmétiques.',
    shortDescription: 'Huile d\'argan pure pressée à froid',
    price: 300,
    originalPrice: 350,
    category: new mongoose.Types.ObjectId('650000000000000000000003'),
    images: [
      '/images/products/huile-argan-1.jpg',
      '/images/products/huile-argan-2.jpg',
      '/images/products/huile-argan-3.jpg'
    ],
    inStock: true,
    stockQuantity: 25,
    featured: true,
    tags: ['bio', 'pressée à froid', 'cosmétique', 'cuisine', 'pure'],
    nutritionalInfo: {
      calories: 884,
      protein: 0,
      carbs: 0,
      fat: 100,
      fiber: 0,
      sugar: 0,
      sodium: 0
    },
    ingredients: ['Huile d\'argan pure (100%)'],
    weight: '250ml',
    dimensions: {
      length: 6,
      width: 6,
      height: 15
    },
    origin: 'Essaouira, Maroc',
    certifications: ['Bio', 'Pressée à Froid', 'Commerce Équitable'],
    storageInstructions: 'Conserver dans un endroit frais et sec, à l\'abri de la lumière directe',
    shelfLife: '18 mois',
    isActive: true,
    seo: {
      title: 'Huile d\'Argan Pure Bio - 250ml | Pressée à Froid',
      description: 'Huile d\'argan 100% pure, pressée à froid. Idéale pour la cuisine et les soins cosmétiques. Qualité premium d\'Essaouira.',
      keywords: ['huile d\'argan pure', 'pressée à froid', 'bio', 'cosmétique', 'essaouira']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('6507f1f77bcf86cd799439014'),
    name: 'Amlou aux Noix',
    slug: {
      fr: 'amlou-aux-noix',
      en: 'walnut-amlou',
      ar: 'اللوز-بالجوز'
    },
    description: 'Variante délicieuse de l\'amlou traditionnel enrichie avec des noix pour plus de saveur et de texture.',
    shortDescription: 'Amlou enrichi aux noix',
    price: 140,
    originalPrice: 170,
    category: new mongoose.Types.ObjectId('650000000000000000000001'),
    images: [
      '/images/products/amlou-noix-1.jpg',
      '/images/products/amlou-noix-2.jpg',
      '/images/products/amlou-noix-3.jpg'
    ],
    inStock: true,
    stockQuantity: 40,
    featured: false,
    tags: ['bio', 'noix', 'artisanal', 'amandes', 'texture'],
    nutritionalInfo: {
      calories: 620,
      protein: 20,
      carbs: 28,
      fat: 52,
      fiber: 14,
      sugar: 22,
      sodium: 6
    },
    ingredients: ['Amandes grillées (45%)', 'Noix (15%)', 'Huile d\'argan pure (25%)', 'Miel naturel (15%)'],
    weight: '250g',
    dimensions: {
      length: 8,
      width: 8,
      height: 6
    },
    origin: 'Essaouira, Maroc',
    certifications: ['Bio', 'Artisanal'],
    storageInstructions: 'Conserver dans un endroit frais et sec, à l\'abri de la lumière',
    shelfLife: '12 mois',
    isActive: true,
    seo: {
      title: 'Amlou aux Noix Bio - 250g | Amandes et Noix',
      description: 'Amlou traditionnel enrichi aux noix. Mélange parfait d\'amandes, noix, huile d\'argan et miel. Texture unique.',
      keywords: ['amlou aux noix', 'amandes', 'noix', 'huile d\'argan', 'artisanal']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('6507f1f77bcf86cd799439015'),
    name: 'Miel d\'Eucalyptus',
    slug: {
      fr: 'miel-eucalyptus',
      en: 'eucalyptus-honey',
      ar: 'عسل-الكافور'
    },
    description: 'Miel d\'eucalyptus aux propriétés antiseptiques, parfait pour les maux de gorge et les affections respiratoires.',
    shortDescription: 'Miel d\'eucalyptus aux propriétés antiseptiques',
    price: 180,
    originalPrice: 220,
    category: new mongoose.Types.ObjectId('650000000000000000000002'),
    images: [
      '/images/products/miel-eucalyptus-1.jpg',
      '/images/products/miel-eucalyptus-2.jpg',
      '/images/products/miel-eucalyptus-3.jpg'
    ],
    inStock: true,
    stockQuantity: 35,
    featured: false,
    tags: ['bio', 'eucalyptus', 'antiseptique', 'thérapeutique', 'respiratoire'],
    nutritionalInfo: {
      calories: 304,
      protein: 0.3,
      carbs: 82,
      fat: 0,
      fiber: 0.2,
      sugar: 82,
      sodium: 4
    },
    ingredients: ['Miel d\'eucalyptus pur (100%)'],
    weight: '500g',
    dimensions: {
      length: 10,
      width: 10,
      height: 12
    },
    origin: 'Région de Rabat, Maroc',
    certifications: ['Bio', 'Propriétés Thérapeutiques'],
    storageInstructions: 'Conserver à température ambiante, à l\'abri de l\'humidité',
    shelfLife: '24 mois',
    isActive: true,
    seo: {
      title: 'Miel d\'Eucalyptus Bio - 500g | Propriétés Antiseptiques',
      description: 'Miel d\'eucalyptus aux propriétés antiseptiques. Parfait pour les maux de gorge et affections respiratoires.',
      keywords: ['miel d\'eucalyptus', 'antiseptique', 'maux de gorge', 'bio', 'thérapeutique']
    }
  },
  {
    _id: new mongoose.Types.ObjectId('6507f1f77bcf86cd799439016'),
    name: 'Huile d\'Argan Cosmétique',
    slug: {
      fr: 'huile-argan-cosmetique',
      en: 'cosmetic-argan-oil',
      ar: 'زيت-الأركان-التجميلي'
    },
    description: 'Huile d\'argan spécialement raffinée pour les soins de la peau et des cheveux, riche en vitamine E.',
    shortDescription: 'Huile d\'argan pour soins cosmétiques',
    price: 250,
    originalPrice: 300,
    category: new mongoose.Types.ObjectId('650000000000000000000003'),
    images: [
      '/images/products/huile-argan-cosmetique-1.jpg',
      '/images/products/huile-argan-cosmetique-2.jpg',
      '/images/products/huile-argan-cosmetique-3.jpg'
    ],
    inStock: true,
    stockQuantity: 20,
    featured: false,
    tags: ['bio', 'cosmétique', 'soins', 'vitamine E', 'cheveux'],
    nutritionalInfo: {
      calories: 884,
      protein: 0,
      carbs: 0,
      fat: 100,
      fiber: 0,
      sugar: 0,
      sodium: 0
    },
    ingredients: ['Huile d\'argan cosmétique raffinée (100%)'],
    weight: '100ml',
    dimensions: {
      length: 5,
      width: 5,
      height: 12
    },
    origin: 'Essaouira, Maroc',
    certifications: ['Bio', 'Cosmétique Naturel', 'Cruelty-Free'],
    storageInstructions: 'Conserver dans un endroit frais et sec, à l\'abri de la lumière directe',
    shelfLife: '18 mois',
    isActive: true,
    seo: {
      title: 'Huile d\'Argan Cosmétique Bio - 100ml | Soins Peau et Cheveux',
      description: 'Huile d\'argan cosmétique raffinée. Riche en vitamine E pour soins de la peau et des cheveux. 100% naturelle.',
      keywords: ['huile d\'argan cosmétique', 'soins peau', 'cheveux', 'vitamine E', 'bio']
    }
  }
];

// Données des clients de test (adaptées au modèle existant)
const customersData = [
  {
    _id: new mongoose.Types.ObjectId('650000000000000000000101'),
    firstName: 'Ahmed',
    lastName: 'Benali',
    email: '<EMAIL>',
    phone: '+212600000001',
    age: '26-35',
    dateOfBirth: new Date('1985-03-15'),
    isActive: true,
    emailVerified: true,
    phoneVerified: false,
    preferredLanguage: 'fr' as const,
    addresses: [], // Sera rempli avec des ObjectIds après création des adresses
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Client de test créé lors du seeding initial'
  },
  {
    _id: new mongoose.Types.ObjectId('650000000000000000000102'),
    firstName: 'Fatima',
    lastName: 'Alaoui',
    email: '<EMAIL>',
    phone: '+212661234567',
    age: '36-45',
    dateOfBirth: new Date('1980-07-22'),
    isActive: true,
    emailVerified: true,
    phoneVerified: true,
    preferredLanguage: 'ar' as const,
    addresses: [],
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Cliente fidèle, préfère les produits bio'
  },
  {
    _id: new mongoose.Types.ObjectId('650000000000000000000103'),
    firstName: 'Youssef',
    lastName: 'Tazi',
    email: '<EMAIL>',
    phone: '+212670987654',
    age: '18-25',
    dateOfBirth: new Date('1998-12-10'),
    isActive: true,
    emailVerified: false,
    phoneVerified: true,
    preferredLanguage: 'en' as const,
    addresses: [],
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Jeune client intéressé par les produits naturels'
  }
];

// Fonction principale de peuplement
async function seedDatabase() {
  try {
    console.log('🌱 Début du peuplement de la base de données...');

    // Connexion à MongoDB
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI non définie dans les variables d\'environnement');
    }

    await mongoose.connect(mongoUri);
    console.log('✅ Connexion à MongoDB Atlas établie');

    // Nettoyer les collections existantes
    console.log('🧹 Nettoyage des collections existantes...');
    await Promise.all([
      Product.deleteMany({}),
      Category.deleteMany({}),
      Customer.deleteMany({}),
      Order.deleteMany({})
    ]);

    // Insérer les catégories
    console.log('📂 Insertion des catégories...');
    await Category.insertMany(categoriesData);
    console.log(`✅ ${categoriesData.length} catégories insérées`);

    // Insérer les produits
    console.log('📦 Insertion des produits...');
    await Product.insertMany(productsData);
    console.log(`✅ ${productsData.length} produits insérés`);

    // Insérer les clients de test
    console.log('👥 Insertion des clients de test...');
    await Customer.insertMany(customersData);
    console.log(`✅ ${customersData.length} clients insérés`);

    console.log('🎉 Peuplement de la base de données terminé avec succès !');

    // Afficher un résumé
    const stats = await Promise.all([
      Category.countDocuments(),
      Product.countDocuments(),
      Customer.countDocuments(),
      Order.countDocuments()
    ]);

    console.log('\n📊 Résumé de la base de données :');
    console.log(`   📂 Catégories: ${stats[0]}`);
    console.log(`   📦 Produits: ${stats[1]}`);
    console.log(`   👥 Clients: ${stats[2]}`);
    console.log(`   🛒 Commandes: ${stats[3]}`);

  } catch (error) {
    console.error('❌ Erreur lors du peuplement :', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnexion de MongoDB');
    process.exit(0);
  }
}

// Exécuter le script si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase();
}

export { seedDatabase, categoriesData, productsData, customersData };
