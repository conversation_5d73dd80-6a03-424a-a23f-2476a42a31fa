#!/usr/bin/env tsx

/**
 * Script de peuplement simplifié de la base de données MongoDB Atlas
 * Compatible avec les modèles existants
 * Usage: npm run seed:simple
 */

import mongoose from 'mongoose';
import { config } from 'dotenv';
import { Product } from '../models/Product.js';
import { Customer } from '../models/Customer.js';
import { Order } from '../models/Order.js';

// Charger les variables d'environnement
config();

// Données des produits compatibles avec le modèle existant
const productsData = [
  {
    title: {
      ar: 'اللوز التقليدي',
      fr: 'Amlou Traditionnel',
      en: 'Traditional Amlou'
    },
    description: {
      ar: 'اللوز الحرفي المحضر وفقاً للتقاليد البربرية مع اللوز المحمص وزيت الأركان الخالص والعسل الطبيعي',
      fr: 'Amlou artisanal préparé selon la tradition berbère avec des amandes grillées, de l\'huile d\'argan pure et du miel naturel.',
      en: 'Artisanal amlou prepared according to Berber tradition with roasted almonds, pure argan oil and natural honey.'
    },
    slug: {
      ar: 'اللوز-التقليدي',
      fr: 'amlou-traditionnel',
      en: 'traditional-amlou'
    },
    price: 120,
    originalPrice: 150,
    discountPercentage: 20,
    category: 'amlou' as const,
    images: [
      '/images/products/amlou-traditionnel-1.jpg',
      '/images/products/amlou-traditionnel-2.jpg',
      '/images/products/amlou-traditionnel-3.jpg'
    ],
    tags: ['bio', 'traditionnel', 'artisanal', 'amandes', 'huile-argan'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 50,
    featured: true,
    weight: '250g',
    origin: 'Essaouira, Maroc',
    isActive: true
  },
  {
    title: {
      ar: 'عسل الجبل',
      fr: 'Miel de Montagne',
      en: 'Mountain Honey'
    },
    description: {
      ar: 'عسل خالص يُحصد من جبال الأطلس، بخصائص علاجية استثنائية',
      fr: 'Miel pur récolté dans les montagnes de l\'Atlas, aux propriétés thérapeutiques exceptionnelles.',
      en: 'Pure honey harvested from the Atlas mountains, with exceptional therapeutic properties.'
    },
    slug: {
      ar: 'عسل-الجبل',
      fr: 'miel-de-montagne',
      en: 'mountain-honey'
    },
    price: 200,
    originalPrice: 250,
    discountPercentage: 20,
    category: 'honey' as const,
    images: [
      '/images/products/miel-montagne-1.jpg',
      '/images/products/miel-montagne-2.jpg',
      '/images/products/miel-montagne-3.jpg'
    ],
    tags: ['bio', 'montagne', 'thérapeutique', 'atlas', 'naturel'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 30,
    featured: true,
    weight: '500g',
    origin: 'Atlas, Maroc',
    isActive: true
  },
  {
    title: {
      ar: 'زيت الأركان الخالص',
      fr: 'Huile d\'Argan Pure',
      en: 'Pure Argan Oil'
    },
    description: {
      ar: 'زيت أركان 100% خالص، معصور على البارد، مثالي للطبخ والعناية التجميلية',
      fr: 'Huile d\'argan 100% pure, pressée à froid, idéale pour la cuisine et les soins cosmétiques.',
      en: '100% pure argan oil, cold-pressed, ideal for cooking and cosmetic care.'
    },
    slug: {
      ar: 'زيت-الأركان-الخالص',
      fr: 'huile-argan-pure',
      en: 'pure-argan-oil'
    },
    price: 300,
    originalPrice: 350,
    discountPercentage: 14,
    category: 'argan' as const,
    images: [
      '/images/products/huile-argan-1.jpg',
      '/images/products/huile-argan-2.jpg',
      '/images/products/huile-argan-3.jpg'
    ],
    tags: ['bio', 'pressée-à-froid', 'cosmétique', 'cuisine', 'pure'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 25,
    featured: true,
    volume: '250ml',
    origin: 'Essaouira, Maroc',
    isActive: true
  },
  {
    title: {
      ar: 'اللوز بالجوز',
      fr: 'Amlou aux Noix',
      en: 'Walnut Amlou'
    },
    description: {
      ar: 'نسخة لذيذة من اللوز التقليدي مُثراة بالجوز لمزيد من النكهة والقوام',
      fr: 'Variante délicieuse de l\'amlou traditionnel enrichie avec des noix pour plus de saveur et de texture.',
      en: 'Delicious variant of traditional amlou enriched with walnuts for more flavor and texture.'
    },
    slug: {
      ar: 'اللوز-بالجوز',
      fr: 'amlou-aux-noix',
      en: 'walnut-amlou'
    },
    price: 140,
    originalPrice: 170,
    discountPercentage: 18,
    category: 'amlou' as const,
    images: [
      '/images/products/amlou-noix-1.jpg',
      '/images/products/amlou-noix-2.jpg',
      '/images/products/amlou-noix-3.jpg'
    ],
    tags: ['bio', 'noix', 'artisanal', 'texture', 'saveur'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 40,
    featured: false,
    weight: '250g',
    origin: 'Essaouira, Maroc',
    isActive: true
  },
  {
    title: {
      ar: 'عسل الكافور',
      fr: 'Miel d\'Eucalyptus',
      en: 'Eucalyptus Honey'
    },
    description: {
      ar: 'عسل الكافور بخصائص مطهرة، مثالي لالتهاب الحلق والأمراض التنفسية',
      fr: 'Miel d\'eucalyptus aux propriétés antiseptiques, parfait pour les maux de gorge et les affections respiratoires.',
      en: 'Eucalyptus honey with antiseptic properties, perfect for sore throats and respiratory ailments.'
    },
    slug: {
      ar: 'عسل-الكافور',
      fr: 'miel-eucalyptus',
      en: 'eucalyptus-honey'
    },
    price: 180,
    originalPrice: 220,
    discountPercentage: 18,
    category: 'honey' as const,
    images: [
      '/images/products/miel-eucalyptus-1.jpg',
      '/images/products/miel-eucalyptus-2.jpg',
      '/images/products/miel-eucalyptus-3.jpg'
    ],
    tags: ['bio', 'eucalyptus', 'antiseptique', 'thérapeutique', 'respiratoire'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 35,
    featured: false,
    weight: '500g',
    origin: 'Rabat, Maroc',
    isActive: true
  },
  {
    title: {
      ar: 'زيت الأركان التجميلي',
      fr: 'Huile d\'Argan Cosmétique',
      en: 'Cosmetic Argan Oil'
    },
    description: {
      ar: 'زيت أركان مكرر خصيصاً للعناية بالبشرة والشعر، غني بفيتامين E',
      fr: 'Huile d\'argan spécialement raffinée pour les soins de la peau et des cheveux, riche en vitamine E.',
      en: 'Argan oil specially refined for skin and hair care, rich in vitamin E.'
    },
    slug: {
      ar: 'زيت-الأركان-التجميلي',
      fr: 'huile-argan-cosmetique',
      en: 'cosmetic-argan-oil'
    },
    price: 250,
    originalPrice: 300,
    discountPercentage: 17,
    category: 'argan' as const,
    images: [
      '/images/products/huile-argan-cosmetique-1.jpg',
      '/images/products/huile-argan-cosmetique-2.jpg',
      '/images/products/huile-argan-cosmetique-3.jpg'
    ],
    tags: ['bio', 'cosmétique', 'soins', 'vitamine-E', 'cheveux'],
    isOrganic: true,
    inStock: true,
    stockQuantity: 20,
    featured: false,
    volume: '100ml',
    origin: 'Essaouira, Maroc',
    isActive: true
  }
];

// Données des clients de test (adaptées au modèle existant)
const customersData = [
  {
    firstName: 'Ahmed',
    lastName: 'Benali',
    email: '<EMAIL>',
    phone: '+212600000001',
    age: '26-35',
    dateOfBirth: new Date('1985-03-15'),
    isActive: true,
    emailVerified: true,
    phoneVerified: false,
    preferredLanguage: 'fr' as const,
    addresses: [],
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Client de test créé lors du seeding initial'
  },
  {
    firstName: 'Fatima',
    lastName: 'Alaoui',
    email: '<EMAIL>',
    phone: '+212661234567',
    age: '36-45',
    dateOfBirth: new Date('1980-07-22'),
    isActive: true,
    emailVerified: true,
    phoneVerified: true,
    preferredLanguage: 'ar' as const,
    addresses: [],
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Cliente fidèle, préfère les produits bio'
  },
  {
    firstName: 'Youssef',
    lastName: 'Tazi',
    email: '<EMAIL>',
    phone: '+212670987654',
    age: '18-25',
    dateOfBirth: new Date('1998-12-10'),
    isActive: true,
    emailVerified: false,
    phoneVerified: true,
    preferredLanguage: 'en' as const,
    addresses: [],
    orders: [],
    totalOrders: 0,
    totalSpent: 0,
    notes: 'Jeune client intéressé par les produits naturels'
  }
];

// Fonction principale de peuplement
async function seedDatabase() {
  try {
    console.log('🌱 Début du peuplement de la base de données...');

    // Connexion à MongoDB
    const mongoUri = process.env.MONGODB_URI;
    if (!mongoUri) {
      throw new Error('MONGODB_URI non définie dans les variables d\'environnement');
    }

    await mongoose.connect(mongoUri);
    console.log('✅ Connexion à MongoDB Atlas établie');

    // Nettoyer les collections existantes
    console.log('🧹 Nettoyage des collections existantes...');
    await Promise.all([
      Product.deleteMany({}),
      Customer.deleteMany({}),
      Order.deleteMany({})
    ]);

    // Insérer les produits
    console.log('📦 Insertion des produits...');
    await Product.insertMany(productsData);
    console.log(`✅ ${productsData.length} produits insérés`);

    // Insérer les clients de test
    console.log('👥 Insertion des clients de test...');
    await Customer.insertMany(customersData);
    console.log(`✅ ${customersData.length} clients insérés`);

    console.log('🎉 Peuplement de la base de données terminé avec succès !');

    // Afficher un résumé
    const stats = await Promise.all([
      Product.countDocuments(),
      Customer.countDocuments(),
      Order.countDocuments()
    ]);

    console.log('\n📊 Résumé de la base de données :');
    console.log(`   📦 Produits: ${stats[0]}`);
    console.log(`   👥 Clients: ${stats[1]}`);
    console.log(`   🛒 Commandes: ${stats[2]}`);

    // Afficher quelques exemples de produits
    console.log('\n🔍 Exemples de produits créés :');
    const sampleProducts = await Product.find().limit(3);
    sampleProducts.forEach(product => {
      console.log(`   - ${product.title.fr} (${product.category}) - ${product.price} MAD`);
    });

  } catch (error) {
    console.error('❌ Erreur lors du peuplement :', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Déconnexion de MongoDB');
    process.exit(0);
  }
}

// Exécuter le script si appelé directement
if (import.meta.url === `file://${process.argv[1]}`) {
  seedDatabase();
}

export { seedDatabase, productsData, customersData };
