import { Request, Response } from 'express';
import { Order } from '../models/Order.js';
import { Customer } from '../models/Customer.js';
import { Product } from '../models/Product.js';
import { OrderQuery, ApiResponse } from '../types/index.js';
import { asyncHandler, sendSuccess, sendError } from '../middleware/errorHandler.js';

// Get all orders with filtering and pagination (Admin only)
export const getOrders = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const {
    page = '1',
    limit = '10',
    sort = 'orderDate',
    order = 'desc',
    status,
    paymentStatus,
    customerId,
    startDate,
    endDate
  } = req.query as OrderQuery;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  // Build query
  const query: any = {};

  if (status) {
    query.orderStatus = status;
  }

  if (paymentStatus) {
    query.paymentStatus = paymentStatus;
  }

  if (customerId) {
    query.customerId = customerId;
  }

  if (startDate || endDate) {
    query.orderDate = {};
    if (startDate) query.orderDate.$gte = new Date(startDate);
    if (endDate) query.orderDate.$lte = new Date(endDate);
  }

  // Build sort object
  const sortObj: any = {};
  sortObj[sort] = order === 'asc' ? 1 : -1;

  // Execute query
  const [orders, total] = await Promise.all([
    Order.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .populate('customerId', 'firstName lastName email phone')
      .lean(),
    Order.countDocuments(query)
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, orders, 'Orders retrieved successfully', 200, pagination);
});

// Get single order by ID
export const getOrderById = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const order = await Order.findById(id)
    .populate('customerId', 'firstName lastName email phone totalOrders totalSpent')
    .populate('items.productId', 'title images category');

  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  sendSuccess(res, order, 'Order retrieved successfully');
});

// Get order by order ID
export const getOrderByOrderId = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { orderId } = req.params;

  const order = await Order.findOne({ orderId })
    .populate('customerId', 'firstName lastName email phone')
    .populate('items.productId', 'title images category');

  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  sendSuccess(res, order, 'Order retrieved successfully');
});

// Create new order
export const createOrder = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const orderData = req.body;

  // Verify customer exists
  const customer = await Customer.findById(orderData.customerId);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  // Verify products exist and have sufficient stock
  for (const item of orderData.items) {
    const product = await Product.findById(item.productId);
    if (!product) {
      return sendError(res, `Product ${item.productTitle} not found`, 404);
    }
    if (!product.canOrder(item.quantity)) {
      return sendError(res, `Insufficient stock for ${item.productTitle}`, 400);
    }
  }

  // Generate unique order ID
  const orderId = await Order.generateOrderId();

  // Create order
  const order = new Order({
    ...orderData,
    orderId
  });

  await order.save();

  // Update product stock
  for (const item of orderData.items) {
    await Product.findByIdAndUpdate(
      item.productId,
      { $inc: { stockQuantity: -item.quantity } }
    );
  }

  // Update customer statistics
  await customer.addOrder(order.total);

  // Add order to customer's orders array
  customer.orders.push(order._id);
  await customer.save();

  // Populate order for response
  const populatedOrder = await Order.findById(order._id)
    .populate('customerId', 'firstName lastName email phone')
    .populate('items.productId', 'title images category');

  sendSuccess(res, populatedOrder, 'Order created successfully', 201);
});

// Update order status
export const updateOrderStatus = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { orderStatus, adminNotes } = req.body;

  const order = await Order.findById(id);
  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  // Update status
  await order.updateStatus(orderStatus, adminNotes);

  sendSuccess(res, order, 'Order status updated successfully');
});

// Update payment status
export const updatePaymentStatus = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { paymentStatus } = req.body;

  const order = await Order.findById(id);
  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  // Update payment status
  await order.updatePaymentStatus(paymentStatus);

  sendSuccess(res, order, 'Payment status updated successfully');
});

// Add tracking number
export const addTrackingNumber = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { trackingNumber } = req.body;

  const order = await Order.findById(id);
  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  // Add tracking number
  await order.addTracking(trackingNumber);

  sendSuccess(res, order, 'Tracking number added successfully');
});

// Cancel order
export const cancelOrder = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { reason } = req.body;

  const order = await Order.findById(id);
  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  // Check if order can be cancelled
  if (['shipped', 'delivered', 'cancelled', 'refunded'].includes(order.orderStatus)) {
    return sendError(res, 'Order cannot be cancelled in current status', 400);
  }

  // Restore product stock
  for (const item of order.items) {
    await Product.findByIdAndUpdate(
      item.productId,
      { $inc: { stockQuantity: item.quantity } }
    );
  }

  // Update order status
  await order.updateStatus('cancelled', reason);

  sendSuccess(res, order, 'Order cancelled successfully');
});

// Get orders by status
export const getOrdersByStatus = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { status } = req.params;
  const { page = '1', limit = '10' } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const [orders, total] = await Promise.all([
    Order.findByStatus(status as any)
      .skip(skip)
      .limit(limitNum)
      .populate('customerId', 'firstName lastName email phone')
      .lean(),
    Order.countDocuments({ orderStatus: status })
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, orders, `${status} orders retrieved successfully`, 200, pagination);
});

// Get orders by date range
export const getOrdersByDateRange = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { startDate, endDate } = req.query;

  if (!startDate || !endDate) {
    return sendError(res, 'Start date and end date are required', 400);
  }

  const orders = await Order.findByDateRange(
    new Date(startDate as string),
    new Date(endDate as string)
  ).populate('customerId', 'firstName lastName email phone');

  sendSuccess(res, orders, 'Orders retrieved successfully');
});

// Get order analytics
export const getOrderAnalytics = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { period = '30' } = req.query;
  const days = parseInt(period as string);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // Order status breakdown
  const statusBreakdown = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    {
      $group: {
        _id: '$orderStatus',
        count: { $sum: 1 },
        totalValue: { $sum: '$total' }
      }
    }
  ]);

  // Payment status breakdown
  const paymentBreakdown = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    {
      $group: {
        _id: '$paymentStatus',
        count: { $sum: 1 },
        totalValue: { $sum: '$total' }
      }
    }
  ]);

  // Daily order trends
  const dailyTrends = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    {
      $group: {
        _id: {
          year: { $year: '$orderDate' },
          month: { $month: '$orderDate' },
          day: { $dayOfMonth: '$orderDate' }
        },
        orderCount: { $sum: 1 },
        totalRevenue: { $sum: '$total' },
        averageOrderValue: { $avg: '$total' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
  ]);

  // Top products
  const topProducts = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    { $unwind: '$items' },
    {
      $group: {
        _id: '$items.productId',
        productTitle: { $first: '$items.productTitle' },
        totalQuantity: { $sum: '$items.quantity' },
        totalRevenue: { $sum: '$items.total' },
        orderCount: { $sum: 1 }
      }
    },
    { $sort: { totalRevenue: -1 } },
    { $limit: 10 }
  ]);

  // Regional breakdown
  const regionalBreakdown = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    {
      $group: {
        _id: '$shippingAddress.region',
        orderCount: { $sum: 1 },
        totalRevenue: { $sum: '$total' },
        averageOrderValue: { $avg: '$total' }
      }
    },
    { $sort: { totalRevenue: -1 } }
  ]);

  // Overall statistics
  const overallStats = await Order.aggregate([
    { $match: { orderDate: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        totalRevenue: { $sum: '$total' },
        averageOrderValue: { $avg: '$total' },
        totalItems: { $sum: { $size: '$items' } }
      }
    }
  ]);

  sendSuccess(res, {
    period: `${days} days`,
    overview: overallStats[0] || {},
    statusBreakdown,
    paymentBreakdown,
    dailyTrends,
    topProducts,
    regionalBreakdown
  }, 'Order analytics retrieved successfully');
});

// Calculate refund
export const calculateRefund = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { refundShipping = false } = req.query;

  const order = await Order.findById(id);
  if (!order) {
    return sendError(res, 'Order not found', 404);
  }

  const refundAmount = order.calculateRefund(refundShipping === 'true');

  sendSuccess(res, {
    orderId: order.orderId,
    refundAmount,
    refundShipping: refundShipping === 'true',
    breakdown: {
      subtotal: order.subtotal,
      tax: order.tax || 0,
      discount: order.discount || 0,
      shippingFee: refundShipping === 'true' ? order.shippingFee : 0
    }
  }, 'Refund amount calculated successfully');
});
