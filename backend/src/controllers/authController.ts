import { Request, Response } from 'express';
import { Admin } from '../models/Admin.js';
import { ApiResponse } from '../types/index.js';
import { asyncHandler, sendSuccess, sendError } from '../middleware/errorHandler.js';
import { generateToken, generateRefreshToken, verifyRefreshToken } from '../middleware/auth.js';

// Admin login
export const login = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { username, password } = req.body;

  // Find admin by username or email
  const admin = await Admin.findByUsernameOrEmail(username);

  if (!admin) {
    return sendError(res, 'Invalid credentials', 401);
  }

  // Check if account is locked
  if (admin.isLocked) {
    return sendError(res, 'Account is temporarily locked due to too many failed login attempts', 423);
  }

  // Check if account is active
  if (!admin.isActive) {
    return sendError(res, 'Account is deactivated', 403);
  }

  // Verify password
  const isPasswordValid = await admin.comparePassword(password);

  if (!isPasswordValid) {
    // Increment login attempts
    await admin.incLoginAttempts();
    return sendError(res, 'Invalid credentials', 401);
  }

  // Reset login attempts on successful login
  if (admin.loginAttempts > 0) {
    await admin.resetLoginAttempts();
  }

  // Update last login
  await admin.updateLastLogin();

  // Generate tokens
  const accessToken = generateToken(admin);
  const refreshToken = generateRefreshToken(admin);

  // Save refresh token
  await admin.addRefreshToken(refreshToken);

  // Remove sensitive data from response
  const adminData = admin.toJSON();
  delete adminData.password;
  delete adminData.refreshTokens;

  sendSuccess(res, {
    admin: adminData,
    accessToken,
    refreshToken
  }, 'Login successful');
});

// Admin logout
export const logout = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { refreshToken } = req.body;
  const admin = req.admin;

  if (refreshToken && admin) {
    // Remove refresh token
    await admin.removeRefreshToken(refreshToken);
  }

  sendSuccess(res, null, 'Logout successful');
});

// Logout from all devices
export const logoutAll = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const admin = req.admin;

  if (admin) {
    // Clear all refresh tokens
    await admin.clearRefreshTokens();
  }

  sendSuccess(res, null, 'Logged out from all devices successfully');
});

// Refresh access token
export const refreshToken = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return sendError(res, 'Refresh token is required', 400);
  }

  try {
    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);

    // Find admin
    const admin = await Admin.findById(decoded.id);

    if (!admin || !admin.isActive) {
      return sendError(res, 'Invalid refresh token', 401);
    }

    // Check if refresh token exists in admin's tokens
    if (!admin.refreshTokens.includes(refreshToken)) {
      return sendError(res, 'Invalid refresh token', 401);
    }

    // Generate new access token
    const newAccessToken = generateToken(admin);

    sendSuccess(res, {
      accessToken: newAccessToken
    }, 'Token refreshed successfully');

  } catch (error) {
    return sendError(res, 'Invalid refresh token', 401);
  }
});

// Get current admin profile
export const getProfile = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const admin = req.admin;

  if (!admin) {
    return sendError(res, 'Admin not found', 404);
  }

  // Remove sensitive data
  const adminData = admin.toJSON();
  delete adminData.password;
  delete adminData.refreshTokens;

  sendSuccess(res, adminData, 'Profile retrieved successfully');
});

// Update admin profile
export const updateProfile = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const admin = req.admin;
  const { firstName, lastName, email } = req.body;

  if (!admin) {
    return sendError(res, 'Admin not found', 404);
  }

  // Check if email already exists (if being updated)
  if (email && email !== admin.email) {
    const existingAdmin = await Admin.findOne({ email, _id: { $ne: admin._id } });
    if (existingAdmin) {
      return sendError(res, 'Email already exists', 409);
    }
  }

  // Update profile
  const updatedAdmin = await Admin.findByIdAndUpdate(
    admin._id,
    { firstName, lastName, email },
    { new: true, runValidators: true }
  );

  if (!updatedAdmin) {
    return sendError(res, 'Failed to update profile', 500);
  }

  // Remove sensitive data
  const adminData = updatedAdmin.toJSON();
  delete adminData.password;
  delete adminData.refreshTokens;

  sendSuccess(res, adminData, 'Profile updated successfully');
});

// Change password
export const changePassword = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const admin = req.admin;
  const { currentPassword, newPassword } = req.body;

  if (!admin) {
    return sendError(res, 'Admin not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await admin.comparePassword(currentPassword);

  if (!isCurrentPasswordValid) {
    return sendError(res, 'Current password is incorrect', 400);
  }

  // Update password
  admin.password = newPassword;
  await admin.save();

  // Clear all refresh tokens to force re-login on all devices
  await admin.clearRefreshTokens();

  sendSuccess(res, null, 'Password changed successfully. Please login again.');
});

// Create admin (Super Admin only)
export const createAdmin = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const adminData = req.body;

  // Check if admin already exists
  const existingAdmin = await Admin.findOne({
    $or: [
      { username: adminData.username },
      { email: adminData.email }
    ]
  });

  if (existingAdmin) {
    return sendError(res, 'Admin with this username or email already exists', 409);
  }

  const admin = new Admin(adminData);
  await admin.save();

  // Remove sensitive data from response
  const responseData = admin.toJSON();
  delete responseData.password;
  delete responseData.refreshTokens;

  sendSuccess(res, responseData, 'Admin created successfully', 201);
});

// Get all admins (Super Admin only)
export const getAdmins = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const {
    page = '1',
    limit = '10',
    sort = 'createdAt',
    order = 'desc',
    role,
    isActive
  } = req.query;

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Build query
  const query: any = {};

  if (role) {
    query.role = role;
  }

  if (isActive === 'true') {
    query.isActive = true;
  } else if (isActive === 'false') {
    query.isActive = false;
  }

  // Build sort object
  const sortObj: any = {};
  sortObj[sort as string] = order === 'asc' ? 1 : -1;

  // Execute query
  const [admins, total] = await Promise.all([
    Admin.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .select('-password -refreshTokens')
      .lean(),
    Admin.countDocuments(query)
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, admins, 'Admins retrieved successfully', 200, pagination);
});

// Update admin (Super Admin only)
export const updateAdmin = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if username or email already exists (if being updated)
  if (updateData.username || updateData.email) {
    const query: any = { _id: { $ne: id } };
    const orConditions: any[] = [];

    if (updateData.username) {
      orConditions.push({ username: updateData.username });
    }
    if (updateData.email) {
      orConditions.push({ email: updateData.email });
    }

    if (orConditions.length > 0) {
      query.$or = orConditions;
      const existingAdmin = await Admin.findOne(query);

      if (existingAdmin) {
        return sendError(res, 'Admin with this username or email already exists', 409);
      }
    }
  }

  const admin = await Admin.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  ).select('-password -refreshTokens');

  if (!admin) {
    return sendError(res, 'Admin not found', 404);
  }

  sendSuccess(res, admin, 'Admin updated successfully');
});

// Delete admin (Super Admin only)
export const deleteAdmin = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const currentAdmin = req.admin;

  // Prevent self-deletion
  if (currentAdmin._id.toString() === id) {
    return sendError(res, 'Cannot delete your own account', 400);
  }

  const admin = await Admin.findByIdAndUpdate(
    id,
    { isActive: false },
    { new: true }
  ).select('-password -refreshTokens');

  if (!admin) {
    return sendError(res, 'Admin not found', 404);
  }

  // Clear all refresh tokens for the deleted admin
  await admin.clearRefreshTokens();

  sendSuccess(res, null, 'Admin deleted successfully');
});
