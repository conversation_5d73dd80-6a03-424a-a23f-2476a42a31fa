import { Request, Response } from 'express';
import { Category } from '../models/Category.js';
import { Product } from '../models/Product.js';
import { ApiResponse, PaginationQuery } from '../types/index.js';
import { asyncHandler } from '../middleware/errorHandler.js';

interface CategoryQuery extends PaginationQuery {
  isActive?: string;
  parentCategory?: string;
  language?: 'ar' | 'fr' | 'en';
}

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
export const getAllCategories = asyncHandler(async (req: Request<{}, ApiResponse, {}, CategoryQuery>, res: Response<ApiResponse>) => {
  const { 
    page = '1', 
    limit = '50', 
    sort = 'sortOrder', 
    order = 'asc',
    isActive,
    parentCategory,
    language = 'fr'
  } = req.query;

  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);
  const skip = (pageNum - 1) * limitNum;

  // Build query
  const query: any = {};
  
  if (isActive !== undefined) {
    query.isActive = isActive === 'true';
  }
  
  if (parentCategory !== undefined) {
    if (parentCategory === 'null' || parentCategory === '') {
      query.parentCategory = null;
    } else {
      query.parentCategory = parentCategory;
    }
  }

  // Build sort object
  const sortObj: any = {};
  if (sort.includes('.')) {
    sortObj[sort] = order === 'desc' ? -1 : 1;
  } else {
    sortObj[sort] = order === 'desc' ? -1 : 1;
  }

  const [categories, total] = await Promise.all([
    Category.find(query)
      .populate('parentCategory', 'name slug')
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Category.countDocuments(query)
  ]);

  const totalPages = Math.ceil(total / limitNum);

  res.status(200).json({
    success: true,
    message: 'Categories retrieved successfully',
    data: categories,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: totalPages
    }
  });
});

// @desc    Get category tree
// @route   GET /api/categories/tree
// @access  Public
export const getCategoryTree = asyncHandler(async (req: Request<{}, ApiResponse, {}, { language?: 'ar' | 'fr' | 'en' }>, res: Response<ApiResponse>) => {
  const { language = 'fr' } = req.query;

  const categories = await Category.find({ isActive: true })
    .sort({ sortOrder: 1, [`name.${language}`]: 1 })
    .lean();

  // Build tree structure
  const categoryMap = new Map();
  const rootCategories: any[] = [];

  // First pass: create map of all categories
  categories.forEach(category => {
    categoryMap.set(category._id.toString(), {
      ...category,
      children: []
    });
  });

  // Second pass: build tree structure
  categories.forEach(category => {
    const categoryWithChildren = categoryMap.get(category._id.toString());
    
    if (category.parentCategory) {
      const parent = categoryMap.get(category.parentCategory.toString());
      if (parent) {
        parent.children.push(categoryWithChildren);
      }
    } else {
      rootCategories.push(categoryWithChildren);
    }
  });

  res.status(200).json({
    success: true,
    message: 'Category tree retrieved successfully',
    data: rootCategories
  });
});

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Public
export const getCategoryById = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const category = await Category.findById(id)
    .populate('parentCategory', 'name slug')
    .lean();

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Get subcategories
  const subcategories = await Category.find({ parentCategory: id, isActive: true })
    .sort({ sortOrder: 1 })
    .lean();

  // Get products count
  const productsCount = await Product.countDocuments({ categoryId: id, isActive: true });

  res.status(200).json({
    success: true,
    message: 'Category retrieved successfully',
    data: {
      ...category,
      subcategories,
      productsCount
    }
  });
});

// @desc    Get products by category
// @route   GET /api/categories/:id/products
// @access  Public
export const getCategoryProducts = asyncHandler(async (req: Request<{ id: string }, ApiResponse, {}, PaginationQuery>, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { 
    page = '1', 
    limit = '20', 
    sort = 'createdAt', 
    order = 'desc'
  } = req.query;

  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);
  const skip = (pageNum - 1) * limitNum;

  // Verify category exists
  const category = await Category.findById(id);
  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Build sort object
  const sortObj: any = {};
  sortObj[sort] = order === 'desc' ? -1 : 1;

  const [products, total] = await Promise.all([
    Product.find({ categoryId: id, isActive: true })
      .populate('categoryId', 'name slug')
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Product.countDocuments({ categoryId: id, isActive: true })
  ]);

  const totalPages = Math.ceil(total / limitNum);

  res.status(200).json({
    success: true,
    message: 'Category products retrieved successfully',
    data: {
      category,
      products,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: totalPages
      }
    }
  });
});

// @desc    Create new category
// @route   POST /api/categories
// @access  Private/Admin
export const createCategory = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const categoryData = req.body;

  // Check if category with same slug already exists
  const existingCategory = await Category.findOne({
    $or: [
      { 'slug.ar': categoryData.slug?.ar },
      { 'slug.fr': categoryData.slug?.fr },
      { 'slug.en': categoryData.slug?.en }
    ]
  });

  if (existingCategory) {
    return res.status(400).json({
      success: false,
      message: 'Category with this slug already exists'
    });
  }

  const category = new Category(categoryData);
  await category.save();

  res.status(201).json({
    success: true,
    message: 'Category created successfully',
    data: category
  });
});

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private/Admin
export const updateCategory = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const updateData = req.body;

  const category = await Category.findById(id);

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Check if updating slug and it conflicts with existing category
  if (updateData.slug) {
    const existingCategory = await Category.findOne({
      _id: { $ne: id },
      $or: [
        { 'slug.ar': updateData.slug.ar },
        { 'slug.fr': updateData.slug.fr },
        { 'slug.en': updateData.slug.en }
      ]
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        message: 'Category with this slug already exists'
      });
    }
  }

  Object.assign(category, updateData);
  await category.save();

  res.status(200).json({
    success: true,
    message: 'Category updated successfully',
    data: category
  });
});

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private/Admin
export const deleteCategory = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const category = await Category.findById(id);

  if (!category) {
    return res.status(404).json({
      success: false,
      message: 'Category not found'
    });
  }

  // Check if category has products
  const productsCount = await Product.countDocuments({ categoryId: id });
  if (productsCount > 0) {
    return res.status(400).json({
      success: false,
      message: `Cannot delete category. It has ${productsCount} products associated with it.`
    });
  }

  // Check if category has subcategories
  const subcategoriesCount = await Category.countDocuments({ parentCategory: id });
  if (subcategoriesCount > 0) {
    return res.status(400).json({
      success: false,
      message: `Cannot delete category. It has ${subcategoriesCount} subcategories.`
    });
  }

  await Category.findByIdAndDelete(id);

  res.status(200).json({
    success: true,
    message: 'Category deleted successfully'
  });
});
