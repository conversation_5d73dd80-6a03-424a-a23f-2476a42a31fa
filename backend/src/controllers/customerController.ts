import { Request, Response } from 'express';
import { Customer } from '../models/Customer.js';
import { Address } from '../models/Address.js';
import { Order } from '../models/Order.js';
import { CustomerQuery, ApiResponse } from '../types/index.js';
import { asyncHandler, sendSuccess, sendError } from '../middleware/errorHandler.js';

// Get all customers with filtering and pagination (Admin only)
export const getCustomers = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const {
    page = '1',
    limit = '10',
    sort = 'createdAt',
    order = 'desc',
    search,
    isActive,
    emailVerified
  } = req.query as CustomerQuery;

  const pageNum = parseInt(page);
  const limitNum = parseInt(limit);
  const skip = (pageNum - 1) * limitNum;

  // Build query
  const query: any = {};

  if (isActive === 'true') {
    query.isActive = true;
  } else if (isActive === 'false') {
    query.isActive = false;
  }

  if (emailVerified === 'true') {
    query.emailVerified = true;
  } else if (emailVerified === 'false') {
    query.emailVerified = false;
  }

  if (search) {
    const searchRegex = new RegExp(search, 'i');
    query.$or = [
      { firstName: searchRegex },
      { lastName: searchRegex },
      { email: searchRegex },
      { phone: searchRegex }
    ];
  }

  // Build sort object
  const sortObj: any = {};
  sortObj[sort] = order === 'asc' ? 1 : -1;

  // Execute query
  const [customers, total] = await Promise.all([
    Customer.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .populate('addresses', 'type city region isDefault')
      .lean(),
    Customer.countDocuments(query)
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, customers, 'Customers retrieved successfully', 200, pagination);
});

// Get single customer by ID (Admin only)
export const getCustomerById = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const customer = await Customer.findById(id)
    .populate('addresses')
    .populate({
      path: 'orders',
      select: 'orderId total orderStatus orderDate',
      options: { sort: { orderDate: -1 }, limit: 10 }
    });

  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  sendSuccess(res, customer, 'Customer retrieved successfully');
});

// Create new customer
export const createCustomer = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const customerData = req.body;

  // Check if customer already exists
  const existingCustomer = await Customer.findOne({
    $or: [
      { email: customerData.email },
      { phone: customerData.phone }
    ]
  });

  if (existingCustomer) {
    return sendError(res, 'Customer with this email or phone already exists', 409);
  }

  const customer = new Customer(customerData);
  await customer.save();

  sendSuccess(res, customer, 'Customer created successfully', 201);
});

// Update customer
export const updateCustomer = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if email or phone already exists (if being updated)
  if (updateData.email || updateData.phone) {
    const query: any = { _id: { $ne: id } };
    const orConditions: any[] = [];

    if (updateData.email) {
      orConditions.push({ email: updateData.email });
    }
    if (updateData.phone) {
      orConditions.push({ phone: updateData.phone });
    }

    if (orConditions.length > 0) {
      query.$or = orConditions;
      const existingCustomer = await Customer.findOne(query);

      if (existingCustomer) {
        return sendError(res, 'Customer with this email or phone already exists', 409);
      }
    }
  }

  const customer = await Customer.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  );

  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  sendSuccess(res, customer, 'Customer updated successfully');
});

// Delete customer (soft delete)
export const deleteCustomer = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const customer = await Customer.findByIdAndUpdate(
    id,
    { isActive: false },
    { new: true }
  );

  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  sendSuccess(res, null, 'Customer deleted successfully');
});

// Get customer addresses
export const getCustomerAddresses = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const { type } = req.query;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  const addresses = await Address.findByCustomer(id, type as string);

  sendSuccess(res, addresses, 'Customer addresses retrieved successfully');
});

// Add customer address
export const addCustomerAddress = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const addressData = req.body;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  const address = new Address({
    ...addressData,
    customerId: id
  });

  await address.save();

  // Add address to customer's addresses array
  customer.addresses.push(address._id);
  await customer.save();

  sendSuccess(res, address, 'Address added successfully', 201);
});

// Update customer address
export const updateCustomerAddress = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id, addressId } = req.params;
  const updateData = req.body;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  const address = await Address.findOneAndUpdate(
    { _id: addressId, customerId: id },
    updateData,
    { new: true, runValidators: true }
  );

  if (!address) {
    return sendError(res, 'Address not found', 404);
  }

  sendSuccess(res, address, 'Address updated successfully');
});

// Delete customer address
export const deleteCustomerAddress = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id, addressId } = req.params;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  const address = await Address.findOneAndUpdate(
    { _id: addressId, customerId: id },
    { isActive: false },
    { new: true }
  );

  if (!address) {
    return sendError(res, 'Address not found', 404);
  }

  sendSuccess(res, null, 'Address deleted successfully');
});

// Get customer orders
export const getCustomerOrders = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;
  const {
    page = '1',
    limit = '10',
    sort = 'orderDate',
    order = 'desc',
    status
  } = req.query;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  // Build query
  const query: any = { customerId: id };
  if (status) {
    query.orderStatus = status;
  }

  // Build sort object
  const sortObj: any = {};
  sortObj[sort as string] = order === 'asc' ? 1 : -1;

  // Execute query
  const [orders, total] = await Promise.all([
    Order.find(query)
      .sort(sortObj)
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Order.countDocuments(query)
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, orders, 'Customer orders retrieved successfully', 200, pagination);
});

// Get customer statistics
export const getCustomerStats = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { id } = req.params;

  const customer = await Customer.findById(id);
  if (!customer) {
    return sendError(res, 'Customer not found', 404);
  }

  // Get order statistics
  const orderStats = await Order.aggregate([
    { $match: { customerId: customer._id } },
    {
      $group: {
        _id: '$orderStatus',
        count: { $sum: 1 },
        totalValue: { $sum: '$total' }
      }
    }
  ]);

  // Get monthly order trends (last 12 months)
  const twelveMonthsAgo = new Date();
  twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

  const monthlyTrends = await Order.aggregate([
    {
      $match: {
        customerId: customer._id,
        orderDate: { $gte: twelveMonthsAgo }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$orderDate' },
          month: { $month: '$orderDate' }
        },
        orderCount: { $sum: 1 },
        totalSpent: { $sum: '$total' }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);

  const stats = {
    customer: customer.getSummary(),
    ordersByStatus: orderStats,
    monthlyTrends,
    totalAddresses: customer.addresses.length
  };

  sendSuccess(res, stats, 'Customer statistics retrieved successfully');
});

// Search customers
export const searchCustomers = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const { q: query, page = '1', limit = '10' } = req.query;

  if (!query) {
    return sendError(res, 'Search query is required', 400);
  }

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const skip = (pageNum - 1) * limitNum;

  const searchRegex = new RegExp(query as string, 'i');
  const searchQuery = {
    $or: [
      { firstName: searchRegex },
      { lastName: searchRegex },
      { email: searchRegex },
      { phone: searchRegex }
    ],
    isActive: true
  };

  const [customers, total] = await Promise.all([
    Customer.find(searchQuery)
      .select('firstName lastName email phone totalOrders totalSpent lastOrderDate')
      .sort({ totalSpent: -1 })
      .skip(skip)
      .limit(limitNum)
      .lean(),
    Customer.countDocuments(searchQuery)
  ]);

  const pagination = {
    page: pageNum,
    limit: limitNum,
    total,
    pages: Math.ceil(total / limitNum)
  };

  sendSuccess(res, customers, 'Search results retrieved successfully', 200, pagination);
});

// Get customer analytics (Admin only)
export const getCustomerAnalytics = asyncHandler(async (req: Request, res: Response<ApiResponse>) => {
  const analytics = await Customer.aggregate([
    {
      $group: {
        _id: null,
        totalCustomers: { $sum: 1 },
        activeCustomers: { $sum: { $cond: ['$isActive', 1, 0] } },
        verifiedEmails: { $sum: { $cond: ['$emailVerified', 1, 0] } },
        verifiedPhones: { $sum: { $cond: ['$phoneVerified', 1, 0] } },
        totalSpent: { $sum: '$totalSpent' },
        totalOrders: { $sum: '$totalOrders' },
        averageOrderValue: { $avg: { $cond: [{ $gt: ['$totalOrders', 0] }, { $divide: ['$totalSpent', '$totalOrders'] }, 0] } }
      }
    }
  ]);

  // Customer tiers
  const tierStats = await Customer.aggregate([
    {
      $addFields: {
        tier: {
          $switch: {
            branches: [
              { case: { $gte: ['$totalSpent', 5000] }, then: 'platinum' },
              { case: { $gte: ['$totalSpent', 2000] }, then: 'gold' },
              { case: { $gte: ['$totalSpent', 500] }, then: 'silver' }
            ],
            default: 'bronze'
          }
        }
      }
    },
    {
      $group: {
        _id: '$tier',
        count: { $sum: 1 },
        totalSpent: { $sum: '$totalSpent' }
      }
    }
  ]);

  // Registration trends (last 12 months)
  const twelveMonthsAgo = new Date();
  twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

  const registrationTrends = await Customer.aggregate([
    {
      $match: {
        createdAt: { $gte: twelveMonthsAgo }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        },
        newCustomers: { $sum: 1 }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);

  sendSuccess(res, {
    overview: analytics[0] || {},
    tierBreakdown: tierStats,
    registrationTrends
  }, 'Customer analytics retrieved successfully');
});
